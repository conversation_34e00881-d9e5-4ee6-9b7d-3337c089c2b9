{"tracks": {"track_0": {"name_key": "track_0_name", "difficulty": 1, "laps": 3, "map_file": "map_0.tmx", "preview_file": "map_0_preview.png", "base_reward": 500, "waypoints": [[100, 350], [100, 120], [250, 120], [250, 200], [600, 200], [600, 120], [850, 120], [850, 450], [700, 450], [700, 350]], "start_pos": [400, 350], "start_angle": 180}, "track_1": {"name_key": "track_1_name", "difficulty": 2, "laps": 4, "map_file": "map_0.tmx", "preview_file": "map_0_preview.png", "base_reward": 1000, "waypoints": [[100, 350], [100, 120], [250, 120], [250, 200], [600, 200], [600, 120], [850, 120], [850, 450], [700, 450], [700, 350]], "start_pos": [400, 350], "start_angle": 180}, "track_2": {"name_key": "track_2_name", "difficulty": 3, "laps": 5, "map_file": "map_0.tmx", "preview_file": "map_0_preview.png", "base_reward": 2000, "waypoints": [[100, 350], [100, 120], [250, 120], [250, 200], [600, 200], [600, 120], [850, 120], [850, 450], [700, 450], [700, 350]], "start_pos": [400, 350], "start_angle": 180}}, "opponents": {"ai_easy": {"name_key": "opponent_easy", "skill": 0.6, "reward_mult": 1.0}, "ai_medium": {"name_key": "opponent_medium", "skill": 0.8, "reward_mult": 1.5}, "ai_hard": {"name_key": "opponent_hard", "skill": 1.0, "reward_mult": 2.0}, "ai_expert": {"name_key": "opponent_expert", "skill": 1.2, "reward_mult": 3.0}}, "cars": {"car_0": {"name_key": "car_0_name", "size_class": "XS", "sprite_index": 0, "price": 5000}, "car_1": {"name_key": "car_1_name", "size_class": "XS", "sprite_index": 1, "price": 6000}, "car_2": {"name_key": "car_2_name", "size_class": "XS", "sprite_index": 2, "price": 7500}, "car_3": {"name_key": "car_3_name", "size_class": "XS", "sprite_index": 3, "price": 6500}, "car_4": {"name_key": "car_4_name", "size_class": "XS", "sprite_index": 4, "price": 5500}, "car_5": {"name_key": "car_5_name", "size_class": "XS", "sprite_index": 5, "price": 4500}, "car_6": {"name_key": "car_6_name", "size_class": "S", "sprite_index": 6, "price": 25000}, "car_7": {"name_key": "car_7_name", "size_class": "S", "sprite_index": 7, "price": 18000}, "car_8": {"name_key": "car_8_name", "size_class": "S", "sprite_index": 8, "price": 35000}, "car_9": {"name_key": "car_9_name", "size_class": "S", "sprite_index": 9, "price": 40000}, "car_10": {"name_key": "car_10_name", "size_class": "S", "sprite_index": 10, "price": 30000}, "car_11": {"name_key": "car_11_name", "size_class": "S", "sprite_index": 11, "price": 32000}, "car_12": {"name_key": "car_12_name", "size_class": "M", "sprite_index": 12, "price": 150000}, "car_13": {"name_key": "car_13_name", "size_class": "M", "sprite_index": 13, "price": 280000}, "car_14": {"name_key": "car_14_name", "size_class": "M", "sprite_index": 14, "price": 320000}, "car_15": {"name_key": "car_15_name", "size_class": "M", "sprite_index": 15, "price": 350000}, "car_16": {"name_key": "car_16_name", "size_class": "M", "sprite_index": 16, "price": 180000}, "car_17": {"name_key": "car_17_name", "size_class": "M", "sprite_index": 17, "price": 200000}}, "parts": {"engines": {"xs_0_engine": {"name_key": "engine_xs_0", "size_class": "XS", "tier": 1, "power": 80, "price": 500}, "xs_1_engine": {"name_key": "engine_xs_1", "size_class": "XS", "tier": 2, "power": 95, "price": 1200}, "xs_2_engine": {"name_key": "engine_xs_2", "size_class": "XS", "tier": 3, "power": 110, "price": 2500}, "s_0_engine": {"name_key": "engine_s_0", "size_class": "S", "tier": 1, "power": 150, "price": 3000}, "s_1_engine": {"name_key": "engine_s_1", "size_class": "S", "tier": 2, "power": 180, "price": 6000}, "s_2_engine": {"name_key": "engine_s_2", "size_class": "S", "tier": 3, "power": 210, "price": 12000}, "m_0_engine": {"name_key": "engine_m_0", "size_class": "M", "tier": 1, "power": 280, "price": 20000}, "m_1_engine": {"name_key": "engine_m_1", "size_class": "M", "tier": 2, "power": 350, "price": 40000}, "m_2_engine": {"name_key": "engine_m_2", "size_class": "M", "tier": 3, "power": 420, "price": 75000}}, "breaks": {"xs_0_breaks": {"name_key": "breaks_xs_0", "size_class": "XS", "tier": 1, "braking": 60, "price": 300}, "xs_1_breaks": {"name_key": "breaks_xs_1", "size_class": "XS", "tier": 2, "braking": 75, "price": 800}, "xs_2_breaks": {"name_key": "breaks_xs_2", "size_class": "XS", "tier": 3, "braking": 90, "price": 1800}, "s_0_breaks": {"name_key": "breaks_s_0", "size_class": "S", "tier": 1, "braking": 100, "price": 2000}, "s_1_breaks": {"name_key": "breaks_s_1", "size_class": "S", "tier": 2, "braking": 120, "price": 4500}, "s_2_breaks": {"name_key": "breaks_s_2", "size_class": "S", "tier": 3, "braking": 140, "price": 9000}, "m_0_breaks": {"name_key": "breaks_m_0", "size_class": "M", "tier": 1, "braking": 160, "price": 15000}, "m_1_breaks": {"name_key": "breaks_m_1", "size_class": "M", "tier": 2, "braking": 190, "price": 30000}, "m_2_breaks": {"name_key": "breaks_m_2", "size_class": "M", "tier": 3, "braking": 220, "price": 55000}}, "boosts": {"xs_0_boost": {"name_key": "boost_xs_0", "size_class": "XS", "tier": 1, "boost_power": 15, "price": 400}, "xs_1_boost": {"name_key": "boost_xs_1", "size_class": "XS", "tier": 2, "boost_power": 25, "price": 1000}, "xs_2_boost": {"name_key": "boost_xs_2", "size_class": "XS", "tier": 3, "boost_power": 35, "price": 2200}, "s_0_boost": {"name_key": "boost_s_0", "size_class": "S", "tier": 1, "boost_power": 40, "price": 2500}, "s_1_boost": {"name_key": "boost_s_1", "size_class": "S", "tier": 2, "boost_power": 55, "price": 5500}, "s_2_boost": {"name_key": "boost_s_2", "size_class": "S", "tier": 3, "boost_power": 70, "price": 11000}, "m_0_boost": {"name_key": "boost_m_0", "size_class": "M", "tier": 1, "boost_power": 80, "price": 18000}, "m_1_boost": {"name_key": "boost_m_1", "size_class": "M", "tier": 2, "boost_power": 100, "price": 35000}, "m_2_boost": {"name_key": "boost_m_2", "size_class": "M", "tier": 3, "boost_power": 120, "price": 65000}}}}
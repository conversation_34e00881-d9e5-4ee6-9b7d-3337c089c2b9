"""
Race Module
===========
Main race gameplay state handling the race loop, UI, and completion.
"""
import math
import pygame as pg
import os
from src.constants import *
from src.game.car import Car
from src.game.ai_driver import AIDriver
from src.core.assets import get_car_sprite, load_image
from src.core.tmx_loader import load_tmx
from src.core.game_data import (
    get_track_data,
    get_opponent_data,
    get_car_stats,
    get_car_size_class
)


class Race:
    """
    Main race gameplay class.
    Manages the race loop, player car, AI opponent, collision detection,
    and race completion.
    """

    def __init__(self, app, player, track_id, opponent_id, on_complete_callback):
        self.app = app
        self.player = player
        self.track_id = track_id
        self.opponent_id = opponent_id
        self.on_complete_callback = on_complete_callback

        # Load track data
        self.track_data = get_track_data(track_id)
        self.opponent_data = get_opponent_data(opponent_id)

        # Race settings
        self.total_laps = self.track_data.get('laps', 3)
        self.waypoints = self.track_data.get('waypoints', [])
        self.start_pos = self.track_data.get('start_pos', [640, 400])
        self.start_angle = self.track_data.get('start_angle', 0)

        # Load TMX map or fallback to image
        self.tmx_map = None
        self.track_image = None
        self._load_track()

        # Create player car
        self.player_car = self._create_player_car()

        # Create AI opponent
        self.ai_driver = self._create_ai_opponent()

        # Race state
        self.state = 'COUNTDOWN'  # COUNTDOWN, RACING, PAUSED, FINISHED
        self.countdown = 3.0
        self.race_time = 0.0
        self.paused = False

        # UI fonts
        self.hud_font = pg.font.SysFont('Consolas', 28, bold=True)
        self.big_font = pg.font.SysFont('Consolas', 72, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)

        # Camera - initialize centered between both cars
        self._init_camera()

        # Results
        self.winner = None
        self.reward = 0

    def _init_camera(self):
        """Initialize camera centered between both cars."""
        screen_w = self.app.screen.get_width()
        screen_h = self.app.screen.get_height()

        # Center between player and AI
        center_x = (self.player_car.x + self.ai_driver.car.x) / 2
        center_y = (self.player_car.y + self.ai_driver.car.y) / 2

        # Set camera to center both cars on screen
        self.camera_x = center_x - screen_w // 2
        self.camera_y = center_y - screen_h // 2

        # Clamp to track bounds
        track_w, track_h = self._get_track_size()
        if track_w > 0:
            max_x = max(0, track_w - screen_w)
            max_y = max(0, track_h - screen_h)
            self.camera_x = max(0, min(self.camera_x, max_x))
            self.camera_y = max(0, min(self.camera_y, max_y))

    def _get_track_size(self):
        """Get the track dimensions in pixels."""
        if self.tmx_map:
            return self.tmx_map.pixel_width, self.tmx_map.pixel_height
        elif self.track_image:
            return self.track_image.get_width(), self.track_image.get_height()
        return 1280, 720

    def _load_track(self):
        """Load track from TMX or PNG file."""
        map_file = self.track_data.get('map_file', 'map_0.tmx')
        map_path = os.path.join('maps', map_file)

        # Try TMX first
        if map_file.endswith('.tmx'):
            self.tmx_map = load_tmx(map_path)
            if self.tmx_map:
                self.track_image = self.tmx_map.rendered_surface
                print(f"[RACE] Loaded TMX map: {map_file}")
                return

        # Fallback to PNG
        png_path = map_path.replace('.tmx', '.png')
        self.track_image = load_image(png_path, alpha=False)

        if not self.track_image:
            # Ultimate fallback: create a simple surface
            self.track_image = pg.Surface((1280, 720))
            self.track_image.fill((50, 50, 50))
            print("[RACE] Warning: Using fallback track surface")

    def _calculate_lane_offset(self, lane_side):
        """
        Calculate x,y offset for positioning cars side-by-side based on start angle.

        Args:
            lane_side: -1 for left lane, +1 for right lane

        Returns:
            (offset_x, offset_y) tuple
        """
        import math
        # The offset should be perpendicular to the driving direction
        # start_angle is the direction cars face (0=right, 90=down, -90=up)
        # Perpendicular is start_angle + 90
        perp_angle = math.radians(self.start_angle + 90)
        lane_offset = 35  # Half the distance between cars (70px total separation)

        offset_x = math.cos(perp_angle) * lane_offset * lane_side
        offset_y = math.sin(perp_angle) * lane_offset * lane_side

        return (offset_x, offset_y)

    def _create_player_car(self):
        """Create the player's car with proper stats."""
        # Get mounted parts from garage
        mounted_parts = {}
        for car_item in self.player.garage:
            if isinstance(car_item, dict):
                car_id = car_item.get('model_id')
                if car_id == self.player.current_car:
                    mounted_parts = car_item.get('mounted_parts', {})
                    break

        # Calculate stats
        stats = get_car_stats(self.player.current_car, mounted_parts)

        # Get sprite
        sprite = get_car_sprite(self.app.assets, self.player.current_car)

        # Create car at start position (left lane)
        offset_x, offset_y = self._calculate_lane_offset(-1)  # -1 = left lane
        start_x = self.start_pos[0] + offset_x
        start_y = self.start_pos[1] + offset_y

        return Car(start_x, start_y, self.start_angle, stats, sprite, is_player=True)

    def _create_ai_opponent(self):
        """Create the AI opponent."""
        skill = self.opponent_data.get('skill', 1.0)

        # AI uses a random car from the same class
        player_class = get_car_size_class(self.player.current_car)
        ai_car_id = f"car_{5 if player_class == 'XS' else (11 if player_class == 'S' else 17)}"

        # Base stats for AI (scaled by skill)
        ai_stats = {
            'max_speed': 180 * skill,
            'acceleration': 130 * skill,
            'braking': 90 * skill,
            'boost_power': 0
        }

        # Get sprite
        sprite = get_car_sprite(self.app.assets, ai_car_id)

        # Create car at start position (right lane)
        offset_x, offset_y = self._calculate_lane_offset(1)  # +1 = right lane
        start_x = self.start_pos[0] + offset_x
        start_y = self.start_pos[1] + offset_y

        ai_car = Car(start_x, start_y, self.start_angle, ai_stats, sprite, is_player=False)

        return AIDriver(ai_car, self.waypoints, skill)

    def update(self, events, dt):
        """Main race update loop."""
        # Handle events
        for event in events:
            if event.type == pg.KEYDOWN:
                if event.key == pg.K_ESCAPE:
                    if self.state == 'RACING':
                        self.state = 'PAUSED'
                    elif self.state == 'PAUSED':
                        self.state = 'RACING'
                    elif self.state == 'FINISHED':
                        self._finish_race()

                if event.key == pg.K_RETURN and self.state == 'FINISHED':
                    self._finish_race()

        if self.state == 'COUNTDOWN':
            self._update_countdown(dt)
        elif self.state == 'RACING':
            self._update_racing(dt)
        elif self.state == 'PAUSED':
            pass  # Do nothing while paused

    def _update_countdown(self, dt):
        """Handle countdown before race starts."""
        self.countdown -= dt

        # Keep camera centered on both cars during countdown
        self._init_camera()

        if self.countdown <= 0:
            self.state = 'RACING'
            self.app.audio.play_sfx('ui_select')

    def _update_racing(self, dt):
        """Update race gameplay."""
        self.race_time += dt

        # Get pressed keys for player input
        keys = pg.key.get_pressed()
        key_dict = {
            pg.K_UP: keys[pg.K_UP],
            pg.K_DOWN: keys[pg.K_DOWN],
            pg.K_LEFT: keys[pg.K_LEFT],
            pg.K_RIGHT: keys[pg.K_RIGHT],
            pg.K_w: keys[pg.K_w],
            pg.K_s: keys[pg.K_s],
            pg.K_a: keys[pg.K_a],
            pg.K_d: keys[pg.K_d],
            pg.K_SPACE: keys[pg.K_SPACE]
        }

        # Update player car with TMX collision
        self.player_car.update(dt, key_dict, self.tmx_map)

        # Update AI with TMX collision
        self.ai_driver.update(dt)
        if self.tmx_map:
            self.ai_driver.car._check_tmx_collision(self.tmx_map)

        # Check car-to-car collision
        self.player_car.check_car_collision(self.ai_driver.car)

        # Check waypoints and laps
        self.player_car.check_waypoint(self.waypoints, self.total_laps)
        self.ai_driver.check_waypoint(self.total_laps)

        # Update camera to follow player
        self._update_camera()

        # Check for race completion
        if self.player_car.finished or self.ai_driver.car.finished:
            self._determine_winner()

    def _update_camera(self):
        """
        Update camera position with smooth follow and dynamic zoom feel.
        Shows both cars when close, smoothly transitions to following player.
        """
        screen_w = self.app.screen.get_width()
        screen_h = self.app.screen.get_height()

        # Calculate distance between cars
        dx = self.player_car.x - self.ai_driver.car.x
        dy = self.player_car.y - self.ai_driver.car.y
        car_distance = math.sqrt(dx * dx + dy * dy)

        # Dynamic blend based on distance and time
        # When cars are close (< 400px), show both; otherwise follow player
        distance_blend = min(1.0, max(0.0, (car_distance - 200) / 300))

        # Time-based transition (first 3 seconds show both, then transition)
        time_blend = min(1.0, max(0.0, (self.race_time - 3.0) / 2.0))

        # Combined blend - use the maximum to prioritize showing both when close
        blend = max(distance_blend, time_blend) if self.race_time > 3.0 else 0.0

        # Center between both cars
        center_x = (self.player_car.x + self.ai_driver.car.x) / 2
        center_y = (self.player_car.y + self.ai_driver.car.y) / 2

        # Blend between center view and player follow
        target_focus_x = center_x * (1 - blend) + self.player_car.x * blend
        target_focus_y = center_y * (1 - blend) + self.player_car.y * blend

        # Add slight look-ahead based on player velocity
        if blend > 0.5:
            angle_rad = math.radians(self.player_car.angle)
            look_ahead = min(100, self.player_car.velocity * 2)
            target_focus_x += math.cos(angle_rad) * look_ahead
            target_focus_y += math.sin(angle_rad) * look_ahead

        target_x = target_focus_x - screen_w // 2
        target_y = target_focus_y - screen_h // 2

        # Smooth camera movement with variable smoothing
        smoothing = 0.08 + blend * 0.07  # 0.08 when showing both, 0.15 when following
        self.camera_x += (target_x - self.camera_x) * smoothing
        self.camera_y += (target_y - self.camera_y) * smoothing

        # Clamp to track bounds (allow slight overflow for edge visibility)
        track_w, track_h = self._get_track_size()
        margin = 50
        max_x = max(-margin, track_w - screen_w + margin)
        max_y = max(-margin, track_h - screen_h + margin)
        self.camera_x = max(-margin, min(self.camera_x, max_x))
        self.camera_y = max(-margin, min(self.camera_y, max_y))

    def _determine_winner(self):
        """Determine the winner and calculate rewards."""
        self.state = 'FINISHED'

        if self.player_car.finished and not self.ai_driver.car.finished:
            self.winner = 'PLAYER'
        elif self.ai_driver.car.finished and not self.player_car.finished:
            self.winner = 'AI'
        elif self.player_car.lap > self.ai_driver.car.lap:
            self.winner = 'PLAYER'
        else:
            self.winner = 'AI'

        # Calculate reward
        if self.winner == 'PLAYER':
            base_reward = self.track_data.get('base_reward', 500)
            mult = self.opponent_data.get('reward_mult', 1.0)
            self.reward = int(base_reward * mult)
        else:
            self.reward = 0

    def _finish_race(self):
        """Apply rewards and return to hub."""
        if self.winner == 'PLAYER' and self.reward > 0:
            self.player.money += self.reward

        self.on_complete_callback()

    def draw(self, screen):
        """Main draw method - full-screen immersive racing view."""
        screen_w = screen.get_width()
        screen_h = screen.get_height()

        # Fill background with dark asphalt color
        screen.fill((35, 35, 40))

        # Draw track
        if self.track_image:
            track_rect = self.track_image.get_rect()
            track_rect.x = -int(self.camera_x)
            track_rect.y = -int(self.camera_y)
            screen.blit(self.track_image, track_rect)

        # Draw cars
        camera_offset = (self.camera_x, self.camera_y)
        self.ai_driver.car.draw(screen, camera_offset)
        self.player_car.draw(screen, camera_offset)

        # Draw minimal overlay HUD (corners only)
        self._draw_overlay_hud(screen)

        # Draw state-specific overlays
        if self.state == 'COUNTDOWN':
            self._draw_countdown(screen)
        elif self.state == 'PAUSED':
            self._draw_paused(screen)
        elif self.state == 'FINISHED':
            self._draw_results(screen)

    def _draw_overlay_hud(self, screen):
        """Draw minimal overlay HUD in screen corners with transparency."""
        screen_w = screen.get_width()
        screen_h = screen.get_height()

        # Create semi-transparent overlay surface for HUD elements
        hud_surface = pg.Surface((screen_w, screen_h), pg.SRCALPHA)

        # === TOP-LEFT: Lap counter and position ===
        panel_w, panel_h = 160, 70
        panel_surf = pg.Surface((panel_w, panel_h), pg.SRCALPHA)
        pg.draw.rect(panel_surf, (0, 0, 0, 140), (0, 0, panel_w, panel_h), border_radius=8)

        # Lap counter
        lap_text = f"LAP {self.player_car.lap + 1}/{self.total_laps}"
        lap_surf = self.hud_font.render(lap_text, True, TEXT_MAIN)
        panel_surf.blit(lap_surf, (10, 8))

        # Position indicator
        if self.player_car.lap > self.ai_driver.car.lap:
            pos = 1
        elif self.player_car.lap < self.ai_driver.car.lap:
            pos = 2
        elif self.player_car.current_waypoint > self.ai_driver.car.current_waypoint:
            pos = 1
        else:
            pos = 2

        pos_color = ACCENT_GREEN if pos == 1 else ACCENT_RED
        pos_text = f"P{pos}"
        pos_surf = self.big_font.render(pos_text, True, pos_color)
        panel_surf.blit(pos_surf, (10, 32))

        hud_surface.blit(panel_surf, (15, 15))

        # === TOP-RIGHT: Race time ===
        minutes = int(self.race_time) // 60
        seconds = int(self.race_time) % 60
        ms = int((self.race_time % 1) * 100)
        time_text = f"{minutes:02d}:{seconds:02d}.{ms:02d}"
        time_surf = self.hud_font.render(time_text, True, TEXT_MAIN)
        time_w = time_surf.get_width() + 20
        time_h = 40

        time_panel = pg.Surface((time_w, time_h), pg.SRCALPHA)
        pg.draw.rect(time_panel, (0, 0, 0, 140), (0, 0, time_w, time_h), border_radius=8)
        time_panel.blit(time_surf, (10, 8))
        hud_surface.blit(time_panel, (screen_w - time_w - 15, 15))

        # === BOTTOM-CENTER: Speed display (large, prominent) ===
        speed = int(self.player_car.get_speed_kmh())
        speed_text = f"{speed}"
        speed_unit = "KM/H"

        speed_surf = self.big_font.render(speed_text, True, ACCENT_GOLD)
        unit_surf = self.info_font.render(speed_unit, True, TEXT_DIM)

        total_w = speed_surf.get_width() + unit_surf.get_width() + 10
        speed_panel_w = total_w + 30
        speed_panel_h = 60

        speed_panel = pg.Surface((speed_panel_w, speed_panel_h), pg.SRCALPHA)
        pg.draw.rect(speed_panel, (0, 0, 0, 120), (0, 0, speed_panel_w, speed_panel_h), border_radius=10)
        speed_panel.blit(speed_surf, (15, 5))
        speed_panel.blit(unit_surf, (speed_surf.get_width() + 20, 25))

        hud_surface.blit(speed_panel, ((screen_w - speed_panel_w) // 2, screen_h - speed_panel_h - 15))

        # === BOTTOM-RIGHT: Boost bar (if applicable) ===
        if self.player_car.boost_power > 0:
            bar_w, bar_h = 150, 20
            bar_panel_w = bar_w + 20
            bar_panel_h = bar_h + 30

            boost_panel = pg.Surface((bar_panel_w, bar_panel_h), pg.SRCALPHA)
            pg.draw.rect(boost_panel, (0, 0, 0, 120), (0, 0, bar_panel_w, bar_panel_h), border_radius=8)

            # Label
            boost_label = self.info_font.render("BOOST", True, TEXT_DIM)
            boost_panel.blit(boost_label, (10, 5))

            # Bar background
            pg.draw.rect(boost_panel, (60, 60, 60), (10, 25, bar_w, bar_h), border_radius=4)

            # Bar fill
            fill_w = int(bar_w * (self.player_car.boost_fuel / 100))
            boost_color = (255, 150, 0) if self.player_car.boost_active else ACCENT_BLUE
            if fill_w > 0:
                pg.draw.rect(boost_panel, boost_color, (10, 25, fill_w, bar_h), border_radius=4)

            # Bar border
            pg.draw.rect(boost_panel, TEXT_DIM, (10, 25, bar_w, bar_h), 1, border_radius=4)

            hud_surface.blit(boost_panel, (screen_w - bar_panel_w - 15, screen_h - bar_panel_h - 15))

        # Blit the entire HUD surface
        screen.blit(hud_surface, (0, 0))

    def _draw_hud(self, screen):
        """Legacy HUD method - redirects to overlay HUD."""
        self._draw_overlay_hud(screen)

    def _draw_minimap(self, screen):
        """Draw a small minimap in the corner showing car positions."""
        # Minimap dimensions
        map_w, map_h = 150, 100
        map_x = screen.get_width() - map_w - 15
        map_y = 70

        # Create minimap surface
        minimap = pg.Surface((map_w, map_h), pg.SRCALPHA)
        pg.draw.rect(minimap, (0, 0, 0, 140), (0, 0, map_w, map_h), border_radius=6)
        pg.draw.rect(minimap, (80, 80, 80), (0, 0, map_w, map_h), 1, border_radius=6)

        # Calculate scale
        track_w, track_h = self._get_track_size()
        scale_x = (map_w - 20) / max(1, track_w)
        scale_y = (map_h - 20) / max(1, track_h)
        scale = min(scale_x, scale_y)

        # Draw player position
        px = int(self.player_car.x * scale) + 10
        py = int(self.player_car.y * scale) + 10
        pg.draw.circle(minimap, ACCENT_BLUE, (px, py), 4)

        # Draw AI position
        ax = int(self.ai_driver.car.x * scale) + 10
        ay = int(self.ai_driver.car.y * scale) + 10
        pg.draw.circle(minimap, ACCENT_RED, (ax, ay), 4)

        screen.blit(minimap, (map_x, map_y))

    def _draw_boost_bar_corner(self, screen):
        """Draw boost bar in bottom-right corner."""
        if self.player_car.boost_power <= 0:
            return

        bar_x = screen.get_width() - 170
        bar_y = screen.get_height() - 50
        bar_w = 150
        bar_h = 20

        # Background panel
        panel_surf = pg.Surface((bar_w + 20, bar_h + 30), pg.SRCALPHA)
        pg.draw.rect(panel_surf, (0, 0, 0, 140), panel_surf.get_rect(), border_radius=8)

        # Fill
        fill_w = int(bar_w * (self.player_car.boost_fuel / 100))
        boost_color = (255, 150, 0) if self.player_car.boost_active else ACCENT_BLUE
        if fill_w > 0:
            pg.draw.rect(panel_surf, boost_color, (10, 25, fill_w, bar_h), border_radius=4)

        # Border
        pg.draw.rect(panel_surf, TEXT_DIM, (10, 25, bar_w, bar_h), 1, border_radius=4)

        # Label
        boost_label = self.info_font.render("BOOST", True, TEXT_MAIN)
        panel_surf.blit(boost_label, (10, 5))

        screen.blit(panel_surf, (bar_x - 10, bar_y - 25))

    def _draw_countdown(self, screen):
        """Draw the countdown overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 100))
        screen.blit(overlay, (0, 0))

        # Countdown number
        count = max(1, int(self.countdown) + 1)
        if self.countdown <= 0:
            text = "GO!"
            color = ACCENT_GREEN
        else:
            text = str(count)
            color = ACCENT_GOLD

        text_surf = self.big_font.render(text, True, color)
        text_rect = text_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2))
        screen.blit(text_surf, text_rect)

    def _draw_paused(self, screen):
        """Draw the pause overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        screen.blit(overlay, (0, 0))

        # Paused text
        text = self.app.lang.get("race_paused")
        text_surf = self.big_font.render(text, True, TEXT_MAIN)
        text_rect = text_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 - 50))
        screen.blit(text_surf, text_rect)

        # Instructions
        resume_text = f"ESC - {self.app.lang.get('race_resume')}"
        resume_surf = self.info_font.render(resume_text, True, TEXT_DIM)
        resume_rect = resume_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 + 30))
        screen.blit(resume_surf, resume_rect)

    def _draw_results(self, screen):
        """Draw the race results overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 200))
        screen.blit(overlay, (0, 0))

        cx = screen.get_width() // 2
        cy = screen.get_height() // 2

        # Finished text
        finished_text = self.app.lang.get("race_finished")
        finished_surf = self.big_font.render(finished_text, True, TEXT_MAIN)
        screen.blit(finished_surf, finished_surf.get_rect(center=(cx, cy - 100)))

        # Win/Lose text
        if self.winner == 'PLAYER':
            result_text = self.app.lang.get("race_you_win")
            result_color = ACCENT_GREEN
        else:
            result_text = self.app.lang.get("race_you_lose")
            result_color = ACCENT_RED

        result_surf = self.big_font.render(result_text, True, result_color)
        screen.blit(result_surf, result_surf.get_rect(center=(cx, cy - 20)))

        # Reward
        if self.reward > 0:
            reward_text = f"{self.app.lang.get('race_reward_earned')}: ${self.reward}"
            reward_surf = self.hud_font.render(reward_text, True, ACCENT_GOLD)
            screen.blit(reward_surf, reward_surf.get_rect(center=(cx, cy + 60)))

        # Time
        minutes = int(self.race_time) // 60
        seconds = int(self.race_time) % 60
        time_text = f"{self.app.lang.get('race_time')}: {minutes:02d}:{seconds:02d}"
        time_surf = self.info_font.render(time_text, True, TEXT_DIM)
        screen.blit(time_surf, time_surf.get_rect(center=(cx, cy + 100)))

        # Continue instruction
        continue_text = f"ENTER - {self.app.lang.get('race_continue')}"
        continue_surf = self.info_font.render(continue_text, True, TEXT_DIM)
        screen.blit(continue_surf, continue_surf.get_rect(center=(cx, cy + 150)))

    def _draw_waypoints(self, screen):
        """Debug: Draw waypoints on the track."""
        for i, wp in enumerate(self.waypoints):
            x = int(wp[0] - self.camera_x)
            y = int(wp[1] - self.camera_y)
            color = ACCENT_GREEN if i == self.player_car.current_waypoint else TEXT_DIM
            pg.draw.circle(screen, color, (x, y), 10)
            label = self.info_font.render(str(i), True, TEXT_MAIN)
            screen.blit(label, (x - 5, y - 5))


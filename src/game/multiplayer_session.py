"""
multiplayer_session.py - Complete Multiplayer Game Session Manager
==================================================================
Manages a multiplayer game session including connection, state synchronization,
and player interactions.
✅ v2.1 - Network Client Fix:
- Changed `NetworkGameClient` import to the correct `NetworkClient` class
  to match the refactored network client module. This ensures that the
  session uses the robust, threaded client for all server communications.
✅ MULTIPLAYER FIX v2.0 - SYNCHRONIZACJA GRACZY:
- Fixed _update_remote_cars() to properly sync other players
- Added remote_cars_map for tracking player_id -> car mapping
- Added _initialize_remote_car() for creating remote player cars
- Added _update_car_position() with interpolation for smooth movement
- Fixed filtering to exclude local player from remote cars
- Race object now properly assigned by app.start_multiplayer_race()
- Added error handling and recovery to lobby state
This file contains ALL methods needed by the game loop.
"""

import asyncio
import threading
import logging
from typing import Optional, Dict, Any, List

from src.network.network_client import NetworkClient

logger = logging.getLogger(__name__)


class MultiplayerSession:
    """
    Complete multiplayer game session handler.
    
    Includes all methods required by the game loop:
    - update(events, dt)
    - draw(screen) / render(screen)
    - handle_event(event)
    - cleanup()
    """
    
    def __init__(self, app, slot_id: int, server_ip: str, server_port: int = 8444):
        """
        Initialize multiplayer session (does not connect yet).
        
        Args:
            app: Main application instance
            slot_id: Save slot ID
            server_ip: Server IP address
            server_port: Server port (default 443)
        """
        self.app = app
        self.slot_id = slot_id
        self.server_ip = server_ip
        self.server_port = server_port
        
        # Create network client (without connecting)
        self.client = NetworkClient(self.server_ip, self.server_port)
        
        # Session state
        self.connected = False
        self.in_lobby = False
        self.in_race = False
        self.ready = False
        
        # Game state
        self.room_id: Optional[int] = None
        self.player_id: Optional[str] = None
        self.players: Dict[str, Dict[str, Any]] = {}
        self.race_state: Optional[Dict] = None
        
        # Race object (will be assigned by app.start_multiplayer_race())
        self.race = None
        
        # ✅ NOWE: Mapowanie player_id -> remote car dla synchronizacji
        self.remote_cars_map: Dict[str, Any] = {}
        
        # Network thread management
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.thread: Optional[threading.Thread] = None

        # Lobby callbacks
        self.on_room_list = None
        self.on_room_update = None
        self.on_player_joined_lobby = None
        self.on_player_left_lobby = None
        self.on_room_assigned_lobby = None

        logger.info(f"MultiplayerSession initialized for {server_ip}:{server_port}")
    
    # ========================================================================
    # Connection Management
    # ========================================================================
    
    def start_network(self):
        """Start the network thread and event loop"""
        if self.thread and self.thread.is_alive():
            return

        self.loop = asyncio.new_event_loop()
        
        def run_loop():
            asyncio.set_event_loop(self.loop)
            try:
                self.loop.run_forever()
            finally:
                # Ensure pending tasks are cleaned up
                try:
                    tasks = asyncio.all_tasks(self.loop)
                    for task in tasks:
                        task.cancel()
                    self.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
                except Exception:
                    pass
                self.loop.close()
                logger.info("Network loop closed")
            
        self.thread = threading.Thread(target=run_loop, daemon=True)
        self.thread.start()
        logger.info("Network thread started")

    def connect_sync(self) -> bool:
        """
        Connect to server (synchronous wrapper).
        Starts the network thread if not running.
        """
        self.start_network()
        
        if not self.loop:
            return False
            
        future = asyncio.run_coroutine_threadsafe(self.start(), self.loop)
        try:
            return future.result(timeout=15.0)
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False

    async def start(self) -> bool:
        """
        Connect to server and start the multiplayer session.
        
        This must be called after __init__() to establish connection.
        
        Returns:
            True if successfully connected
        """
        logger.info(f"Starting multiplayer session, connecting to {self.server_ip}:{self.server_port}")
        
        # Setup callbacks BEFORE connecting
        self._setup_callbacks()
        
        # Connect to server (this is now a sync call that manages its own threads)
        success = self.client.connect()
        
        if success:
            self.connected = True
            # Assuming the 'connected' message from server will set the player_id
            logger.info("✅ Connection process started successfully!")
        else:
            logger.error("❌ Failed to connect to multiplayer server")
        
        return success
    
    def _setup_callbacks(self):
        """Setup network client callbacks"""
        self.client.set_callbacks(on_message=self._handle_network_message, on_disconnect=self._on_disconnected)
    
    # ========================================================================
    # Network Event Handlers
    # ========================================================================
    
    def _handle_network_message(self, message: dict):
        """Unified message handler to delegate server messages."""
        msg_type = message.get('type')
        if msg_type == 'connected':
            self._on_connected(message.get('player_id'))
        elif msg_type == 'game_state':
            self._handle_game_state(message)
        elif msg_type == 'room_assigned':
            self._handle_room_assigned(message.get('room_id'))
            # Also forward to lobby if callback is set
            if self.on_room_assigned_lobby:
                self.on_room_assigned_lobby(message)
        elif msg_type == 'race_start_signal':
            self._handle_race_start(message)
        elif msg_type == 'player_joined':
            self._handle_player_joined(message.get('player_id'))
            # Also forward to lobby if callback is set
            if self.on_player_joined_lobby:
                self.on_player_joined_lobby(message)
        elif msg_type == 'player_left':
            self._handle_player_left(message.get('player_id'))
            # Also forward to lobby if callback is set
            if self.on_player_left_lobby:
                self.on_player_left_lobby(message)
        elif msg_type == 'room_list':
            self._handle_room_list(message)
        # Add other message types as needed

    def _on_connected(self, player_id: str):
        """Called when successfully connected to server"""
        logger.info(f"Connection established, player ID: {player_id}")
        self.connected = True
        self.player_id = player_id
    
    def _on_disconnected(self):
        """Called when disconnected from server"""
        logger.info(f"Disconnected from server")
        self.connected = False
        self.in_lobby = False
        self.in_race = False

    def _handle_room_list(self, message: dict):
        """Handle room list update from server"""
        rooms = message.get('rooms', [])
        logger.info(f"Received room list: {len(rooms)} rooms")

        # Log room details for debugging
        for room in rooms:
            logger.info(f"  Room {room.get('room_id')}: {room.get('room_name')} ({room.get('current_players')}/{room.get('max_players')})")

        if self.on_room_list:
            logger.info("Forwarding room list to lobby callback")
            self.on_room_list(message)
        else:
            logger.warning("No room list callback set!")
    
    def _handle_game_state(self, state: Dict[str, Any]):
        """
        Handle game state update from server.
        
        Called ~60 times per second during race.
        """
        self.race_state = state
        
        # Update player states
        players_data = state.get('players', {})
        for player_id, player_state in players_data.items():
            self.players[player_id] = player_state
        
        logger.debug(f"Game state received: seq={state.get('sequence_number')}, players={len(players_data)}")
    
    def _handle_room_assigned(self, room_id: int):
        """Called when assigned to a room"""
        logger.info(f"Assigned to room {room_id}")
        self.room_id = room_id
        self.in_lobby = True
    
    def _handle_race_start(self, race_info: Dict[str, Any]):
        """
        ✅ FIXED: Called when race starts. Triggers the main app to switch to the race state.
        
        Args:
            race_info: Dictionary containing race start information from server
                       Should include 'map_id', 'laps', 'race_type', and 'checkpoints'
        """
        logger.info("🏁 Race start signal received from server!")
        
        # Zmień stan na wyścig
        self.in_race = True
        self.in_lobby = False
        
        # ✅ FIX: Wyczyść mapę samochodów, aby wymusić ich ponowne utworzenie dla nowego wyścigu
        self.remote_cars_map.clear()
        
        # Pobierz ID mapy z serwera (Task 2.3: Use map_id)
        map_id = race_info.get('map_id', '0')
        logger.info(f"[MULTIPLAYER] Map ID from server: {map_id}")
        
        # ✅ KLUCZOWA POPRAWKA: Wywołaj metodę która teraz poprawnie przypisuje wyścig
        try:
            self.app.start_multiplayer_race(race_info)
        except Exception as e:
            logger.error(f"[MULTIPLAYER] ❌ Failed to start race: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Przywróć stan lobby
            self.in_race = False
            self.in_lobby = True
            return
        
        # ✅ Dodatkowa walidacja - sprawdź czy wyścig został utworzony
        if not hasattr(self, 'race') or self.race is None:
            logger.error("[MULTIPLAYER] ❌ Race object not created properly!")
            logger.error("[MULTIPLAYER] self.race is None - race will not start!")
            # Przywróć stan lobby
            self.in_race = False
            self.in_lobby = True
            return
        
        logger.info("[MULTIPLAYER] ✅ Race object successfully created and assigned")
        logger.info(f"[MULTIPLAYER] ✅ Race track: {self.race.track_id}")
        logger.info(f"[MULTIPLAYER] ✅ Race player car: {self.race.player_car}")

    def on_race_complete(self):
        """Callback when race finishes"""
        logger.info("Race finished")
        # Return to lobby
        self.in_race = False
        self.in_lobby = True
        self.ready = False
        self.race = None
        
        # Clear remote cars mapping
        self.remote_cars_map.clear()
    
    def _handle_player_joined(self, player_id: str):
        """Called when another player joins the room"""
        logger.info(f"Player joined: {player_id}")
        self.players[player_id] = {
            'player_id': player_id,
            'ready': False
        }
    
    def _handle_player_left(self, player_id: str):
        """Called when a player leaves the room"""
        logger.info(f"Player left: {player_id}")
        if player_id in self.players:
            del self.players[player_id]
        
        # ✅ Usuń również remote car jeśli istnieje
        if player_id in self.remote_cars_map:
            car = self.remote_cars_map.pop(player_id)
            
            # KRYTYCZNA POPRAWKA: Usuń z grup Pygame
            if hasattr(self, 'race') and self.race:
                if hasattr(self.race, 'all_sprites'):
                    self.race.all_sprites.remove(car)
                if hasattr(self.race, 'collision_group'):
                    self.race.collision_group.remove(car)
                
                if hasattr(self.race, 'remote_cars') and car in self.race.remote_cars:
                    self.race.remote_cars.remove(car)
                    
            logger.info(f"Removed remote car for player {player_id}")
    
    # ========================================================================
    # Game Loop Methods (Required by app.py)
    # ========================================================================
    
    def update(self, events: List, dt: float):
        """
        Update multiplayer session (called every frame by game loop).
        
        Args:
            events: List of pygame events
            dt: Delta time in seconds
        """
        if not self.connected:
            return
        
        # Handle events
        for event in events:
            self.handle_event(event)
        
        # Update based on current state
        if self.in_lobby:
            self._update_lobby(dt)
        elif self.in_race:
            self._update_race(dt)
    
    def draw(self, screen=None):
        """
        Draw/render multiplayer UI elements (called every frame).
        
        Args:
            screen: Pygame display surface (optional)
        """
        if not self.connected:
            return
        
        if self.in_lobby:
            self._draw_lobby(screen)
        elif self.in_race:
            self._draw_race_ui(screen)
    
    def render(self, screen=None):
        """
        Alias for draw() - some game loops use render() instead.
        
        Args:
            screen: Pygame display surface (optional)
        """
        self.draw(screen)
    
    def handle_event(self, event):
        """
        Handle a single pygame event.
        
        Args:
            event: Pygame event
        """
        # Handle multiplayer-specific events
        pass
    
    def cleanup(self):
        """Cleanup resources when exiting multiplayer"""
        logger.info("Cleaning up multiplayer session")
        self.stop()
    
    def stop(self):
        """Stop session and network thread"""
        self.client.disconnect()
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        self.thread = None
        self.loop = None
        logger.info("Network resources cleaned up.")

    def _update_lobby(self, dt: float):
        """Update lobby state"""
        pass
    
    def _update_race(self, dt: float):
        """
        ✅ POPRAWIONA WERSJA - Update race state and synchronize remote players
        """
        if not hasattr(self, 'race') or not self.race:
            return
            
        # Update local race logic
        self.race.update([], dt)
        
        # Send input to server
        if self.race.player_car:
            throttle = self.race.player_car.throttle
            steering = self.race.player_car.steering
            is_braking = self.race.player_car.is_braking  # ✅ POPRAWKA: Car używa is_braking
            self.send_input(throttle, steering, is_braking)
        
        # ✅ KLUCZOWA POPRAWKA: Zaktualizuj samochody innych graczy
        self._update_remote_cars(dt)

    def _update_remote_cars(self, dt: float):
        """
        ✅ POPRAWIONA WERSJA - Aktualizuje pozycje samochodów innych graczy
        
        Główne poprawki:
        1. Prawidłowe filtrowanie własnego gracza
        2. Mapowanie player_id -> car object
        3. Tworzenie nowych samochodów dla nowych graczy
        4. Interpolacja pozycji dla płynnego ruchu
        5. Usuwanie samochodów graczy którzy opuścili wyścig
        """
        if not self.race_state or not self.race:
            logger.debug("Cannot update remote cars - missing race_state or race")
            return
        
        # Pobierz dane graczy z serwera
        players_state = self.race_state.get('players', {})
        
        # ✅ FIX 1: Odfiltruj własnego gracza
        other_players = {}
        for pid, player_data in players_state.items():
            if pid != self.player_id:
                other_players[pid] = player_data
        
        if not other_players:
            logger.debug("No other players in race")
            return
        
        logger.debug(f"Updating remote cars for {len(other_players)} players")
        
        # ✅ FIX 2: Inicjalizuj remote_cars jeśli nie istnieje
        if not hasattr(self.race, 'remote_cars'):
            self.race.remote_cars = []
        
        # ✅ FIX 3: Utwórz lub zaktualizuj samochody dla każdego gracza
        for player_id, player_data in other_players.items():
            # Sprawdź czy ten gracz już ma samochód
            if player_id not in self.remote_cars_map:
                # Utwórz nowy samochód dla tego gracza
                car = self._initialize_remote_car(player_id, player_data)
                if car:
                    self.remote_cars_map[player_id] = car
                    self.race.remote_cars.append(car)

                    # KRYTYCZNA POPRAWKA: Dodaj do grup Pygame
                    if hasattr(self.race, 'all_sprites'):
                        self.race.all_sprites.add(car)
                    if hasattr(self.race, 'collision_group'):
                        self.race.collision_group.add(car)

                    logger.info(f"✅ Created remote car for player {player_id}. Added to Pygame groups. Group size: {len(self.race.all_sprites.sprites())}")
                else:
                    logger.error(f"❌ Failed to create remote car for player {player_id}")
                    continue
            
            # Zaktualizuj pozycję samochodu
            car = self.remote_cars_map.get(player_id)
            if car:
                # ✅ FIX: Ensure car is in the current race's render list (Sync Fix)
                if hasattr(self.race, 'remote_cars') and car not in self.race.remote_cars:
                    self.race.remote_cars.append(car)
                    logger.warning(f"⚠️ Re-added remote car for {player_id} to race.remote_cars (Sync Recovery)")

                self._update_car_position(car, player_data, dt)
        
        # ✅ FIX 4: Usuń samochody graczy, którzy opuścili wyścig
        players_to_remove = []
        for player_id in self.remote_cars_map.keys():
            if player_id not in other_players:
                players_to_remove.append(player_id)
        
        for player_id in players_to_remove:
            car = self.remote_cars_map.pop(player_id)
            if car in self.race.remote_cars:
                self.race.remote_cars.remove(car)
            logger.info(f"Removed remote car for player {player_id}")

    def _initialize_remote_car(self, player_id: str, player_data: Dict[str, Any]):
        """
        ✅ NOWA METODA - Tworzy samochód dla zdalnego gracza
        
        Args:
            player_id: ID gracza
            player_data: Dane gracza z serwera (x, y, angle, car_model, etc.)
            
        Returns:
            Obiekt Car lub None jeśli się nie powiódł
        """
        from src.game.car import Car
        from src.core.assets import get_car_sprite
        from src.core.game_data import get_car_stats
        
        try:
            # ✅ Pobierz model samochodu z danych gracza
            # Jeśli serwer nie wysyła car_model, użyj domyślnego
            car_model = player_data.get('car_model', 'car_0')
            
            logger.info(f"Creating remote car for {player_id} with model {car_model}")
            
            # Pobierz statystyki i sprite
            stats = get_car_stats(car_model)
            sprite = get_car_sprite(self.app.assets, car_model)
            
            if sprite is None:
                logger.error(f"Failed to load sprite for car_model: {car_model}")
                return None
            
            # Pobierz początkową pozycję
            start_x = player_data.get('x', 0)
            start_y = player_data.get('y', 0)
            start_angle = player_data.get('angle', 0)
            
            # Utwórz samochód (is_player=False - to nie jest lokalny gracz)
            car = Car(
                start_x, 
                start_y, 
                start_angle, 
                stats, 
                sprite, 
                is_player=False
            )
            
            # ✅ Oznacz jako zdalny samochód
            car.remote_player_id = player_id
            car.is_remote = True
            
            logger.info(f"✅ Remote car created: {player_id} at ({start_x}, {start_y})")
            logger.info(f"[DEBUG] Race remote_cars count: {len(self.race.remote_cars) + 1}")
            
            return car
            
        except Exception as e:
            logger.error(f"Failed to create remote car for {player_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _update_car_position(self, car, player_data: Dict[str, Any], dt: float):
        """
        ✅ NOWA METODA - Aktualizuje pozycję zdalnego samochodu z interpolacją
        
        Args:
            car: Obiekt Car do zaktualizowania
            player_data: Dane gracza z serwera
            dt: Delta time
        """
        try:
            # Pobierz docelową pozycję z serwera
            target_x = player_data.get('x', car.x)
            target_y = player_data.get('y', car.y)
            target_angle = player_data.get('angle', car.angle)
            
            # ✅ INTERPOLACJA dla płynniejszego ruchu
            # Zamiast skakać bezpośrednio do pozycji, płynnie się przemieszczamy
            interpolation_speed = 10.0  # Możesz dostosować (wyższe = szybsze)
            
            # Interpoluj pozycję X i Y
            car.x += (target_x - car.x) * interpolation_speed * dt
            car.y += (target_y - car.y) * interpolation_speed * dt
            
            # Interpoluj kąt (uwaga na wrap-around 360°)
            angle_diff = target_angle - car.angle
            # Normalizuj różnicę kąta do -180..180
            while angle_diff > 180:
                angle_diff -= 360
            while angle_diff < -180:
                angle_diff += 360
            car.angle += angle_diff * interpolation_speed * dt
            
            # Zaktualizuj również position vector (używany przez Car.draw)
            car.position.x = car.x
            car.position.y = car.y
            
            # ✅ Opcjonalnie: zaktualizuj prędkość jeśli serwer ją wysyła
            if 'velocity_x' in player_data:
                car.velocity.x = player_data['velocity_x']
            if 'velocity_y' in player_data:
                car.velocity.y = player_data['velocity_y']
            
            # ✅ Zaktualizuj stan wyścigu jeśli dostępny
            if 'lap' in player_data:
                car.lap = player_data['lap']
            if 'current_waypoint' in player_data:
                car.current_waypoint = player_data['current_waypoint']
            if 'finished' in player_data:
                car.finished = player_data['finished']
            
            logger.debug(f"Updated car {car.remote_player_id} to ({car.x:.1f}, {car.y:.1f})")
                
        except Exception as e:
            logger.error(f"Error updating car position: {e}")
    
    def _draw_lobby(self, screen):
        """Draw lobby UI"""
        pass
    
    def _draw_race_ui(self, screen):
        """Draw race UI elements"""
        if hasattr(self, 'race') and self.race:
            self.race.draw(screen)
    
    # ========================================================================
    # Game Actions
    # ========================================================================
    
    def send_input(self, throttle: float, steering: float, is_braking: float):
        """
        Send player input to server.
        
        Call this every frame during race.
        
        Args:
            throttle: 0.0 to 1.0
            steering: -1.0 to 1.0
            is_braking: 0.0 to 1.0
        """
        if self.connected and self.in_race:
            self.client.send_message({
                "type": "input",
                "throttle": throttle,
                "steering": steering,
                "is_braking": is_braking
            })
    
    def send_ready(self):
        """Signal that player is ready to start the race"""
        if self.connected and self.in_lobby:
            self.client.send_message({"type": "ready"})
            self.ready = True
            logger.info("Sent ready signal")
    
    def send_player_info(self, car_model: str):
        """
        Send player info (car selection) to server.
        
        Args:
            car_model: Car model name (e.g., "car_0")
        """
        if self.connected:
            self.client.send_message({"type": "player_info", "car_model": car_model})
            logger.info(f"Sent player info: {car_model}")
    
    def ping(self):
        """Send ping to measure latency"""
        if self.connected:
            self.client.send_message({"type": "ping"})
    
    def disconnect(self):
        """Disconnect from multiplayer server"""
        if self.connected:
            logger.info("Disconnecting from multiplayer session")
            self.client.disconnect()
            
            self.connected = False
            self.in_lobby = False
            self.in_race = False
            self.ready = False
            self.players.clear()
            self.remote_cars_map.clear()
    
    # ========================================================================
    # State Queries
    # ========================================================================
    
    def is_connected(self) -> bool:
        """Check if connected to server"""
        return self.connected
    
    def is_in_lobby(self) -> bool:
        """Check if in multiplayer lobby"""
        return self.in_lobby
    
    def is_in_race(self) -> bool:
        """Check if currently racing"""
        return self.in_race
    
    def is_ready(self) -> bool:
        """Check if player marked as ready"""
        return self.ready
    
    def get_player_id(self) -> Optional[str]:
        """Get local player ID"""
        return self.player_id
    
    def get_room_id(self) -> Optional[int]:
        """Get current room ID"""
        return self.room_id
    
    def get_player_count(self) -> int:
        """Get number of players in room"""
        return len(self.players)
    
    def get_players(self) -> Dict[str, Dict[str, Any]]:
        """Get all players in room"""
        return self.players.copy()
    
    def get_race_state(self) -> Optional[Dict]:
        """Get latest race state from server"""
        return self.race_state
    
    def get_other_players_states(self) -> Dict[str, Dict[str, Any]]:
        """
        Get states of other players (excluding self).
        
        Returns:
            Dict mapping player_id to player state
        """
        if not self.race_state:
            return {}
        
        players_data = self.race_state.get('players', {})
        return {
            pid: state 
            for pid, state in players_data.items() 
            if pid != self.player_id
        }
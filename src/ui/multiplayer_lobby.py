"""
multiplayer_lobby.py - Enhanced Multiplayer Lobby Interface
==========================================================
Advanced UI for multiplayer lobby with:
- Scrollable room list (max 20 rooms displayed)
- Real-time updates when players join/leave
- Join/Spectate functionality
- Create room functionality
- Empty and full room display
- Pagination support
- Chat system (lobby + room chat)

✅ v2.3 FIX: Fixed setup_lobby_callbacks to use on_room_list callback
"""

import pygame
import logging
import asyncio
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class Room:
    """Room information"""
    room_id: int
    name: str
    players_count: int
    max_players: int = 2
    host_name: str = "Unknown"
    track_name: str = "Track 1"
    state: str = "Empty"
    is_full: bool = False
    is_empty: bool = False
    
    @property
    def occupancy_text(self) -> str:
        return f"{self.players_count}/{self.max_players}"
    
    @property
    def can_join(self) -> bool:
        return not self.is_full and self.state in ["Empty", "NotReady"]


class MultiplayerLobby:
    """
    Enhanced multiplayer lobby interface.
    
    Features:
    - Up to 20 rooms displayed at once
    - Real-time room updates
    - Join/Spectate functionality
    - Create room dialog
    - Pagination for >20 rooms
    - Chat system
    """
    
    MAX_DISPLAYED_ROOMS = 20
    
    def __init__(self, screen: pygame.Surface, session):
        self.screen = screen
        self.session = session  # MultiplayerSession instance
        
        # UI State
        self.selected_room_id: Optional[int] = None
        self.in_room = False
        self.rooms: Dict[int, Room] = {}
        self.total_rooms = 0
        self.current_page = 0
        
        # Create room dialog
        self.show_create_dialog = False
        self.create_room_name = ""
        self.create_track_name = "map_0.tmx"
        self.available_maps = ["map_0.tmx"]
        self.selected_map_index = 0
        
        # Chat
        self.chat_messages: List[str] = []
        self.chat_input = ""
        self.chat_active = False
        
        # Colors
        self.BG_COLOR = (20, 20, 30)
        self.PANEL_COLOR = (30, 30, 45)
        self.ACCENT_COLOR = (100, 150, 255)
        self.TEXT_COLOR = (255, 255, 255)
        self.GREEN = (50, 200, 50)
        self.RED = (200, 50, 50)
        self.YELLOW = (255, 200, 50)
        self.ORANGE = (255, 165, 0)
        self.GRAY = (100, 100, 100)
        
        # Fonts
        try:
            self.font_large = pygame.font.Font(None, 48)
            self.font_medium = pygame.font.Font(None, 32)
            self.font_small = pygame.font.Font(None, 24)
            self.font_tiny = pygame.font.Font(None, 20)
        except pygame.error:
            self.font_large = pygame.font.SysFont(None, 48)
            self.font_medium = pygame.font.SysFont(None, 32)
            self.font_small = pygame.font.SysFont(None, 24)
            self.font_tiny = pygame.font.SysFont(None, 20)
        
        # UI Elements
        self.scroll_offset = 0
        self.rooms_per_page = 6
        
        # Buttons
        self.create_room_button = pygame.Rect(20, 650, 150, 40)
        self.refresh_button = pygame.Rect(180, 650, 150, 40)
        self.prev_page_button = pygame.Rect(340, 650, 80, 40)
        self.next_page_button = pygame.Rect(430, 650, 80, 40)
        
        logger.info("MultiplayerLobby initialized for static rooms")
        self.last_refresh_time = 0
        self.refresh_interval = 15  # seconds
        # Initial request for room list
        self.request_room_list_threaded()
    
    def _run_async(self, coro):
        """Helper to run async coroutine on session's event loop"""
        if hasattr(self.session, 'loop') and self.session.loop:
            asyncio.run_coroutine_threadsafe(coro, self.session.loop)
        else:
            logger.warning("Session loop not available")
    
    async def _request_room_list(self, page: int = 0):
        """Request room list from server"""
        if self.session.is_connected():
            logger.info(f"Sending request_room_list message to server (page {page})")
            self.session.client.send_message({
                'type': 'request_room_list',
                'page': page
            })
            logger.info(f"Requested room list page {page} from server")
    
    def request_room_list_threaded(self, page: int = 0):
        """Request room list (safe for main thread)"""
        if self.session.is_connected():
            self._run_async(self._request_room_list(page))

    def update_room(self, room_data: Dict):
        """Update a single room's data."""
        room_id = room_data.get('room_id')
        if room_id is None:
            return

        room = Room(
            room_id=room_id,
            name=room_data.get('room_name', f"Room {room_id}"),
            players_count=room_data.get('current_players', 0),
            max_players=room_data.get('max_players', 2),
            host_name=room_data.get('host_name', 'Unknown'),
            track_name=room_data.get('map_name', 'Track 1'),
            state=room_data.get('status', 'Empty'),
        )
        room.is_full = room.players_count >= room.max_players
        room.is_empty = room.players_count == 0
        self.rooms[room_id] = room
    
    def set_total_rooms(self, total: int):
        """Set total number of rooms available"""
        self.total_rooms = total
    
    def add_chat_message(self, message: str):
        """Add message to chat"""
        self.chat_messages.append(message)
        
        # Keep last 50 messages
        if len(self.chat_messages) > 50:
            self.chat_messages.pop(0)
    
    def handle_event(self, event: pygame.event.Event):
        """Handle pygame events"""
        if self.show_create_dialog:
            self._handle_create_dialog_event(event)
            return
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                if self.chat_active:
                    # Send chat message
                    if self.chat_input.strip():
                        if self.session.is_connected():
                            self._run_async(self._send_chat_message(self.chat_input))
                        self.chat_input = ""
                    self.chat_active = False
                else:
                    # Activate chat
                    self.chat_active = True
            
            elif event.key == pygame.K_ESCAPE:
                if self.chat_active:
                    self.chat_active = False
                    self.chat_input = ""
                elif self.in_room:
                    # Leave room
                    if self.session.is_connected():
                        self._run_async(self._leave_room())
                else:
                    # Back to main menu (handled by app.py)
                    pass
            
            elif event.key == pygame.K_BACKSPACE:
                if self.chat_active:
                    self.chat_input = self.chat_input[:-1]
            
            elif event.key == pygame.K_SPACE:
                if not self.chat_active and self.in_room:
                    # Toggle ready
                    if self.session.is_connected():
                        self.session.send_ready()
            
            # elif event.key == pygame.K_c and not self.chat_active and not self.in_room:
            #     # Open create room dialog
            #     self.show_create_dialog = True
            #     self.create_room_name = ""
            
            elif self.chat_active:
                # Add character to chat input
                if event.unicode and len(self.chat_input) < 100:
                    self.chat_input += event.unicode
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self._handle_click(event.pos)
            
            elif event.button == 4:  # Scroll up
                self.scroll_offset = max(0, self.scroll_offset - 1)
            
            elif event.button == 5:  # Scroll down
                max_scroll = max(0, len(self.rooms) - self.rooms_per_page)
                self.scroll_offset = min(max_scroll, self.scroll_offset + 1)
    
    def _handle_create_dialog_event(self, event: pygame.event.Event):
        """Handle events for create room dialog"""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                # Create room
                if self.create_room_name.strip():
                    if self.session.is_connected():
                        self._run_async(self._create_room(
                            self.create_room_name.strip(),
                            self.create_track_name
                        ))
                    self.show_create_dialog = False
            
            elif event.key == pygame.K_ESCAPE:
                # Cancel
                self.show_create_dialog = False
            
            elif event.key == pygame.K_BACKSPACE:
                self.create_room_name = self.create_room_name[:-1]
            
            elif event.key == pygame.K_TAB:
                # Cycle through available maps
                self.selected_map_index = (self.selected_map_index + 1) % len(self.available_maps)
                self.create_track_name = self.available_maps[self.selected_map_index]
            
            elif event.unicode and len(self.create_room_name) < 30:
                self.create_room_name += event.unicode
    
    def _handle_click(self, pos):
        """Handle mouse click"""
        x, y = pos
        
        # Check button clicks (not in room)
        if not self.in_room:
            # if self.create_room_button.collidepoint(pos):
            #     self.show_create_dialog = True
            #     return
            
            if self.refresh_button.collidepoint(pos):
                self.request_room_list_threaded(self.current_page)
                return
            
            if self.prev_page_button.collidepoint(pos) and self.current_page > 0:
                self.current_page -= 1
                self.request_room_list_threaded(self.current_page)
                return
            
            max_pages = (self.total_rooms + self.MAX_DISPLAYED_ROOMS - 1) // self.MAX_DISPLAYED_ROOMS
            if self.next_page_button.collidepoint(pos) and self.current_page < max_pages - 1:
                self.current_page += 1
                self.request_room_list_threaded(self.current_page)
                return
        
        # Room list area (left panel)
        if x < 600 and not self.in_room:
            # Calculate which room was clicked
            room_y_start = 150
            room_height = 80
            
            room_index = (y - room_y_start) // room_height + self.scroll_offset
            
            sorted_rooms = sorted(self.rooms.values(), key=lambda r: r.room_id)
            if 0 <= room_index < len(sorted_rooms):
                room = sorted_rooms[room_index]
                
                # Check if clicked on spectate button (right side of room item)
                if x > 520:
                    # Spectate
                    if self.session.is_connected():
                        self._run_async(self._spectate_room(room.room_id))
                elif room.can_join:
                    # Join room
                    self.selected_room_id = room.room_id
                    if self.session.is_connected():
                        self._run_async(self._join_room(room.room_id))
                else:
                    if room.is_full:
                        self.add_chat_message(f"Room {room.room_id} is full!")
                    elif room.state != "waiting":
                        self.add_chat_message(f"Room {room.room_id} is already racing!")
    
    async def _create_room(self, room_name: str, track_name: str):
        """Create a new room"""
        if not self.session.is_connected():
            self.add_chat_message("Error: Not connected to server")
            return

        logger.info(f"Creating room: {room_name}")
        
        # ✅ FIX: Ensure track_name is a valid ID, not a filename
        # The UI might be passing "map_0.tmx", but server expects "track_0"
        track_id = track_name
        if track_name.endswith('.tmx'):
            # Simple mapping: map_0.tmx -> track_0
            try:
                # Extract number from map_X.tmx
                import re
                match = re.search(r'map_(\d+)', track_name)
                if match:
                    track_id = f"track_{match.group(1)}"
                else:
                    track_id = "track_0" # Fallback
            except Exception:
                track_id = "track_0"
        
        self.session.client.send_message({
            'type': 'create_room',
            'room_name': room_name,
            'host_name': self.session.player_id[:12] if self.session.player_id else "Unknown",
            'track_name': track_name, # Display name
            'track_id': track_id      # Logic ID for server
        })
        
        self.add_chat_message(f"Creating room '{room_name}'...")
    
    async def _join_room(self, room_id: int):
        """Join a room"""
        if not self.session.is_connected():
            self.add_chat_message("Error: Not connected to server")
            return

        logger.info(f"Joining room {room_id}")
        
        self.session.client.send_message({
            'type': 'join_room',
            'room_id': room_id
        })
        
        self.in_room = True
        self.add_chat_message(f"Joining room {room_id}...")
    
    async def _spectate_room(self, room_id: int):
        """Spectate a room"""
        if not self.session.is_connected():
            self.add_chat_message("Error: Not connected to server")
            return

        logger.info(f"Spectating room {room_id}")
        
        self.session.client.send_message({
            'type': 'spectate_room',
            'room_id': room_id
        })
        
        self.add_chat_message(f"Spectating room {room_id}...")
    
    async def _leave_room(self):
        """Leave current room"""
        if not self.session.is_connected():
            self.in_room = False
            self.add_chat_message("Disconnected from room")
            return

        logger.info("Leaving room")
        
        self.session.client.send_message({
            'type': 'leave_room'
        })
        
        self.in_room = False
        self.add_chat_message("Left room")
    
    async def _send_chat_message(self, message: str):
        """Send chat message"""
        if not self.session.is_connected():
            self.add_chat_message("Error: Not connected to server")
            return

        logger.info(f"Sending chat: {message}")
        
        self.session.client.send_message({
            'type': 'chat_message',
            'message': message,
            'in_room': self.in_room
        })
        
        # Add to local chat immediately
        player_name = self.session.player_id[:12] if self.session.player_id else "You"
        self.add_chat_message(f"{player_name}: {message}")
    
    def update(self, dt: float):
        """Update lobby state"""
        # Periodically request room list if connected and not in room
        current_time = pygame.time.get_ticks() / 1000  # in seconds
        if self.session.is_connected() and not self.in_room:
            if current_time - self.last_refresh_time > self.refresh_interval:
                logger.info("Auto-refreshing room list...")
                self.request_room_list_threaded(self.current_page)
                self.last_refresh_time = current_time
    
    def cleanup(self):
        """Cleanup lobby resources"""
        logger.info("Cleaning up multiplayer lobby")
        self.rooms.clear()
        self.chat_messages.clear()
    
    def draw(self):
        """Draw lobby UI"""
        # Clear screen
        self.screen.fill(self.BG_COLOR)
        
        if self.show_create_dialog:
            self._draw_create_dialog()
        elif self.in_room:
            self._draw_room_view()
        else:
            self._draw_lobby_view()
        
        # Draw chat (always visible)
        self._draw_chat()
    
    def _draw_lobby_view(self):
        """Draw lobby (room list) view"""
        # Title
        title = self.font_large.render("Multiplayer Lobby", True, self.TEXT_COLOR)
        self.screen.blit(title, (20, 20))
        
        # Connection Status
        if not self.session.is_connected():
            status_text = self.font_medium.render("DISCONNECTED", True, self.RED)
            self.screen.blit(status_text, (400, 25))
        
        # Instructions
        instructions = self.font_tiny.render(
            "Click room to join | Right side to spectate | C to create | ENTER for chat | ESC to exit",
            True, (150, 150, 150)
        )
        self.screen.blit(instructions, (20, 80))
        
        # Room count info
        max_pages = max(1, (self.total_rooms + self.MAX_DISPLAYED_ROOMS - 1) // self.MAX_DISPLAYED_ROOMS)
        room_info = self.font_small.render(
            f"Showing {len(self.rooms)} of {self.total_rooms} rooms (Page {self.current_page + 1}/{max_pages})",
            True, self.GRAY
        )
        self.screen.blit(room_info, (20, 105))
        
        # Room list panel
        panel_rect = pygame.Rect(20, 140, 580, 490)
        pygame.draw.rect(self.screen, self.PANEL_COLOR, panel_rect, border_radius=10)
        
        # Draw rooms
        room_y = 150
        
        sorted_rooms = sorted(self.rooms.values(), key=lambda r: r.room_id)
        visible_rooms = sorted_rooms[self.scroll_offset:self.scroll_offset + self.rooms_per_page]
        for room in visible_rooms:
            self._draw_room_item(room, 30, room_y)
            room_y += 80
        
        # No rooms message
        if len(self.rooms) == 0:
            no_rooms_text = self.font_medium.render("Connecting...", True, (150, 150, 150))
            text_rect = no_rooms_text.get_rect(center=(310, 370))
            self.screen.blit(no_rooms_text, text_rect)
            
            create_hint = self.font_small.render("Waiting for room data from server", True, self.GREEN)
            hint_rect = create_hint.get_rect(center=(310, 410))
            self.screen.blit(create_hint, hint_rect)
        
        # Draw buttons
        # self._draw_button(self.create_room_button, "Create Room", self.GREEN)
        self._draw_button(self.refresh_button, "Refresh", self.ACCENT_COLOR)
        self._draw_button(self.prev_page_button, "< Prev", self.ACCENT_COLOR if self.current_page > 0 else self.GRAY)
        self._draw_button(self.next_page_button, "Next >", self.ACCENT_COLOR if self.current_page < max_pages - 1 else self.GRAY)
        
        # Scroll indicator
        if len(self.rooms) > self.rooms_per_page:
            scroll_text = self.font_tiny.render(
                f"Scroll: {self.scroll_offset + 1}-{min(self.scroll_offset + self.rooms_per_page, len(self.rooms))} / {len(self.rooms)}",
                True, (150, 150, 150)
            )
            self.screen.blit(scroll_text, (520, 635))
    
    def _draw_button(self, rect: pygame.Rect, text: str, color):
        """Draw a button"""
        pygame.draw.rect(self.screen, color, rect, border_radius=5)
        pygame.draw.rect(self.screen, self.TEXT_COLOR, rect, 2, border_radius=5)
        
        text_surf = self.font_small.render(text, True, self.TEXT_COLOR)
        text_rect = text_surf.get_rect(center=rect.center)
        self.screen.blit(text_surf, text_rect)
    
    def _draw_room_item(self, room: Room, x: int, y: int):
        """Draw single room in list"""
        # Room background - color based on state
        if room.is_full:
            color = (60, 40, 40)  # Red tint (full)
            status_color = self.RED
            status_text = "FULL"
        elif room.state == "Empty":
            color = (40, 60, 40)  # Green tint (empty)
            status_color = self.GREEN
            status_text = "EMPTY"
        elif room.state == "Racing":
            color = (60, 60, 30)  # Yellow-brown tint (racing)
            status_color = self.ORANGE
            status_text = "RACING"
        elif room.state == "Ready":
            color = (40, 40, 60)  # Blue tint (ready)
            status_color = self.ACCENT_COLOR
            status_text = "READY"
        else:
            color = (50, 50, 60)  # Blue tint (waiting/not ready)
            status_color = self.YELLOW
            status_text = "WAITING"
        
        room_rect = pygame.Rect(x, y, 560, 70)
        pygame.draw.rect(self.screen, color, room_rect, border_radius=8)
        pygame.draw.rect(self.screen, self.ACCENT_COLOR, room_rect, 2, border_radius=8)
        
        # Room name
        room_name_text = self.font_medium.render(f"{room.name}", True, self.TEXT_COLOR)
        self.screen.blit(room_name_text, (x + 10, y + 10))
        
        # Room ID (small, top right)
        room_id_text = self.font_tiny.render(f"#{room.room_id}", True, self.GRAY)
        self.screen.blit(room_id_text, (x + 530, y + 5))
        
        # Occupancy
        occ_text = self.font_medium.render(f"{room.occupancy_text}", True, status_color)
        self.screen.blit(occ_text, (x + 370, y + 10))
        
        # Status
        status_surf = self.font_small.render(status_text, True, status_color)
        self.screen.blit(status_surf, (x + 450, y + 13))
        
        # Host & Track
        info_text = self.font_tiny.render(
            f"Host: {room.host_name} | {room.track_name}",
            True, (180, 180, 180)
        )
        self.screen.blit(info_text, (x + 10, y + 45))
        
        # Action hint
        if room.can_join:
            action_text = self.font_tiny.render("Click to JOIN", True, self.GREEN)
            self.screen.blit(action_text, (x + 370, y + 48))
        elif room.is_full or room.state == "Racing":
            action_text = self.font_tiny.render("→ SPECTATE", True, self.ORANGE)
            self.screen.blit(action_text, (x + 470, y + 48))
    
    def _draw_create_dialog(self):
        """Draw create room dialog"""
        # Darken background
        overlay = pygame.Surface(self.screen.get_size())
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # Dialog box
        dialog_rect = pygame.Rect(250, 200, 500, 300)
        pygame.draw.rect(self.screen, self.PANEL_COLOR, dialog_rect, border_radius=15)
        pygame.draw.rect(self.screen, self.ACCENT_COLOR, dialog_rect, 3, border_radius=15)
        
        # Title
        title = self.font_large.render("Create Room", True, self.TEXT_COLOR)
        title_rect = title.get_rect(center=(500, 250))
        self.screen.blit(title, title_rect)
        
        # Input field
        input_rect = pygame.Rect(300, 320, 400, 40)
        pygame.draw.rect(self.screen, (40, 40, 50), input_rect, border_radius=5)
        pygame.draw.rect(self.screen, self.ACCENT_COLOR, input_rect, 2, border_radius=5)
        
        input_text = self.font_medium.render(
            self.create_room_name + "_",
            True, self.TEXT_COLOR
        )
        self.screen.blit(input_text, (310, 325))
        
        # Instructions
        inst1 = self.font_small.render("Enter room name:", True, (200, 200, 200))
        self.screen.blit(inst1, (300, 295))
        
        # Map Selection
        map_label = self.font_small.render("Map:", True, (200, 200, 200))
        self.screen.blit(map_label, (300, 380))
        
        map_rect = pygame.Rect(360, 375, 340, 35)
        pygame.draw.rect(self.screen, (40, 40, 50), map_rect, border_radius=5)
        pygame.draw.rect(self.screen, self.ACCENT_COLOR, map_rect, 2, border_radius=5)
        
        map_text = self.font_small.render(self.create_track_name, True, self.TEXT_COLOR)
        self.screen.blit(map_text, (370, 382))
        
        # Dropdown arrow
        arrow_points = [(670, 385), (690, 385), (680, 395)]
        pygame.draw.polygon(self.screen, self.TEXT_COLOR, arrow_points)
        
        inst2 = self.font_small.render("Press ENTER to create, ESC to cancel", True, (150, 150, 150))
        inst2_rect = inst2.get_rect(center=(500, 440))
        self.screen.blit(inst2, inst2_rect)
    
    def _draw_room_view(self):
        """Draw room (waiting for players) view"""
        # Title
        room_title = self.font_large.render(f"Room {self.session.room_id or '?'}", True, self.TEXT_COLOR)
        self.screen.blit(room_title, (20, 20))
        
        # Instructions
        instructions = self.font_small.render(
            "SPACE to ready | ENTER for chat | ESC to leave room",
            True, (150, 150, 150)
        )
        self.screen.blit(instructions, (20, 80))
        
        # Players panel
        panel_rect = pygame.Rect(20, 120, 580, 400)
        pygame.draw.rect(self.screen, self.PANEL_COLOR, panel_rect, border_radius=10)
        
        # Draw players
        players = self.session.get_players()
        player_y = 140
        
        for i, (player_id, player_data) in enumerate(players.items()):
            self._draw_player_item(player_id, player_data, 30, player_y)
            player_y += 60
        
        # Ready status
        if self.session.is_ready():
            ready_text = self.font_medium.render("✓ READY", True, self.GREEN)
            self.screen.blit(ready_text, (250, 540))
        else:
            ready_text = self.font_medium.render("Press SPACE to ready up", True, self.YELLOW)
            self.screen.blit(ready_text, (180, 540))
    
    def _draw_player_item(self, player_id: str, player_data: Dict, x: int, y: int):
        """Draw player in room"""
        # Background
        is_ready = player_data.get('ready', False)
        color = (40, 60, 40) if is_ready else (60, 40, 40)
        
        player_rect = pygame.Rect(x, y, 560, 50)
        pygame.draw.rect(self.screen, color, player_rect, border_radius=5)
        
        # Player name
        is_local = player_id == self.session.player_id
        name = "You" if is_local else player_id[:12]
        
        name_text = self.font_medium.render(name, True, self.TEXT_COLOR)
        self.screen.blit(name_text, (x + 10, y + 12))
        
        # Ready indicator
        if is_ready:
            ready_text = self.font_medium.render("✓ READY", True, self.GREEN)
            self.screen.blit(ready_text, (x + 450, y + 12))
        else:
            waiting_text = self.font_medium.render("WAITING...", True, self.YELLOW)
            self.screen.blit(waiting_text, (x + 420, y + 12))
    
    def _draw_chat(self):
        """Draw chat panel"""
        # Chat panel (right side)
        chat_rect = pygame.Rect(620, 120, 380, 570)
        pygame.draw.rect(self.screen, self.PANEL_COLOR, chat_rect, border_radius=10)
        
        # Chat title
        chat_title = self.font_medium.render("Chat", True, self.TEXT_COLOR)
        self.screen.blit(chat_title, (630, 130))
        
        # Chat messages
        msg_y = 170
        visible_messages = self.chat_messages[-20:]  # Last 20 messages
        
        for message in visible_messages:
            # Wrap long messages
            if len(message) > 45:
                lines = [message[i:i+45] for i in range(0, len(message), 45)]
                for line in lines[:2]:  # Max 2 lines per message
                    msg_surface = self.font_tiny.render(line, True, (220, 220, 220))
                    self.screen.blit(msg_surface, (630, msg_y))
                    msg_y += 22
            else:
                msg_surface = self.font_tiny.render(message, True, (220, 220, 220))
                self.screen.blit(msg_surface, (630, msg_y))
                msg_y += 22
        
        # Chat input
        input_rect = pygame.Rect(630, 650, 360, 35)
        input_color = self.ACCENT_COLOR if self.chat_active else (80, 80, 80)
        pygame.draw.rect(self.screen, input_color, input_rect, 2, border_radius=5)
        
        if self.chat_active:
            input_text = self.font_small.render(
                f"> {self.chat_input}_",
                True, self.TEXT_COLOR
            )
        else:
            input_text = self.font_small.render(
                "Press ENTER to chat",
                True, (120, 120, 120)
            )
        
        self.screen.blit(input_text, (635, 655))


def setup_lobby_callbacks(session, lobby):
    """Setup callbacks to integrate lobby with session"""

    def on_room_list(message):
        """Handle a full room list update."""
        rooms_data = message.get('rooms', [])
        logger.info(f"Lobby received full room list: {len(rooms_data)} rooms")

        # Clear existing rooms and update with the new list
        lobby.rooms.clear()
        for room_data in rooms_data:
            logger.info(f"  Processing room: {room_data}")
            lobby.update_room(room_data)

        # Update total room count for pagination
        lobby.set_total_rooms(message.get('total_rooms', len(rooms_data)))
        logger.info(f"Lobby now has {len(lobby.rooms)} rooms, total: {lobby.total_rooms}")

    def on_room_update(message):
        """Handle a single room data update."""
        logger.info(f" lobby received room update: {message}")
        lobby.update_room(message)

    def on_chat_message(message):
        """Handle incoming chat message"""
        sender = message.get('sender', 'Unknown')
        text = message.get('message', '')
        lobby.add_chat_message(f"{sender}: {text}")
    
    def on_player_joined(message):
        """Handle player joined"""
        player_id = message.get('player_id', 'Unknown')
        lobby.add_chat_message(f"{player_id[:12]} joined the room")
    
    def on_player_left(message):
        """Handle player left"""
        player_id = message.get('player_id', 'Unknown')
        lobby.add_chat_message(f"{player_id[:12]} left the room")
    
    def on_room_assigned(message):
        """Handle room assignment"""
        room_id = message.get('room_id')
        lobby.in_room = True
        lobby.selected_room_id = room_id
    
    # Set lobby callbacks on session (not on client)
    session.on_room_list = on_room_list
    session.on_room_update = on_room_update
    session.on_player_joined_lobby = on_player_joined
    session.on_player_left_lobby = on_player_left
    session.on_room_assigned_lobby = on_room_assigned
import os
import sys
import json
import pygame
from typing import Tuple, List, Optional
import platform

# --- NETWORK SETTINGS ---
SERVER_PORT: int = 8443
SERVER_HOST: str = '0.0.0.0'

# --- DEBUG SETTINGS ---
DEBUG_LOGGING: bool = True

from src.core.path import get_user_data_dir

# --- PATH CONFIGURATION ---
if getattr(sys, 'frozen', False):
    BASE_DIR: str = sys._MEIPASS
else:
    BASE_DIR: str = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

DATA_DIR: str = os.path.join(BASE_DIR, 'data')
ASSETS_DIR: str = os.path.join(BASE_DIR, 'assets')
MAPS_DIR: str = os.path.join(BASE_DIR, 'maps')
USER_DATA_DIR: str = get_user_data_dir()

# --- GAME CONSTANTS ---
FPS: int = 60

# --- GAME INFO ---
APP_NAME: str = 'CA-Racing'
APP_VERSION: str = '1.0.0-demo'

# --- GRAPHICS SETTINGS ---
RESOLUTIONS: List[Tuple[int, int]] = [
    (1280, 720),
    (1366, 768),
    (1600, 900),
    (1920, 1080)
]

# ✅ CRITICAL FIX: Lazy initialization of display-dependent constants
# These will be initialized by GameApp.__init__() AFTER pygame.display.set_mode()
_display_initialized: bool = False
_native_w: Optional[int] = None
_native_h: Optional[int] = None
SCREEN_WIDTH: int = 1920  # Default fallback
SCREEN_HEIGHT: int = 1080  # Default fallback
FULLSCREEN: bool = True


def init_display_constants() -> None:
    """
    Initialize display-dependent constants.
    
    ✅ CRITICAL: This MUST be called AFTER pygame.display.set_mode()
    in GameApp.__init__(), not at module import time!
    
    This was the root cause of PyInstaller build failures - pygame.display.init()
    was being called during module import, before any display existed.
    """
    global _display_initialized, _native_w, _native_h, SCREEN_WIDTH, SCREEN_HEIGHT, FULLSCREEN, RESOLUTIONS
    
    if _display_initialized:
        return  # Already initialized
    
    try:
        # Now it's safe to query display info
        info = pygame.display.Info()
        _native_w = info.current_w
        _native_h = info.current_h
        
        # Add native resolution if not already in list
        if (_native_w, _native_h) not in RESOLUTIONS:
            RESOLUTIONS.append((_native_w, _native_h))
        
        # Try to load settings
        _settings_path = os.path.join(USER_DATA_DIR, 'settings.json')
        SCREEN_WIDTH = _native_w
        SCREEN_HEIGHT = _native_h
        FULLSCREEN = True
        
        if os.path.exists(_settings_path):
            try:
                with open(_settings_path, 'r') as f:
                    _data = json.load(f)
                    _idx = _data.get("resolution_idx", -1)
                    if 0 <= _idx < len(RESOLUTIONS):
                        SCREEN_WIDTH, SCREEN_HEIGHT = RESOLUTIONS[_idx]
                    
                    if "fullscreen" in _data:
                        FULLSCREEN = _data["fullscreen"]
            except Exception:
                pass  # Use defaults
        
        _display_initialized = True
        
    except Exception as e:
        # Fallback to safe defaults if display query fails
        print(f"[CONSTANTS] Warning: Could not query display info: {e}")
        SCREEN_WIDTH = 1920
        SCREEN_HEIGHT = 1080
        FULLSCREEN = True
        _display_initialized = True


FPS_LIMITS: List[int] = [30, 60, 120, 144, 0]  # 0 = Unlimited

# --- COLORS (R, G, B) ---
BG_COLOR: Tuple[int, int, int] = (30, 30, 30)
PANEL_BG: Tuple[int, int, int] = (50, 50, 55)
TEXT_MAIN: Tuple[int, int, int] = (255, 255, 255)
TEXT_DIM: Tuple[int, int, int] = (150, 150, 150)

ACCENT_GREEN: Tuple[int, int, int] = (50, 200, 50)
ACCENT_RED: Tuple[int, int, int] = (200, 50, 50)
ACCENT_BLUE: Tuple[int, int, int] = (50, 100, 200)
ACCENT_GOLD: Tuple[int, int, int] = (255, 215, 0)

BUTTON_COLOR: Tuple[int, int, int] = (70, 70, 80)
BUTTON_HOVER_COLOR: Tuple[int, int, int] = (100, 100, 120)

# --- CAR STATUS & PHYSICS ---
TEMP_AMBIENT: float = 25.0
TEMP_OPTIMAL: float = 90.0
TEMP_WARNING: float = 115.0
TEMP_OVERHEAT: float = 130.0
TEMP_FAILURE: float = 150.0

# Degradation Rates (per second/event)
WEAR_TIRE_NORMAL: float = 0.2
WEAR_TIRE_DRIFT: float = 1.5
WEAR_BRAKE_NORMAL: float = 0.5
WEAR_SUSPENSION_IMPACT: float = 5.0

# Performance Penalties (max factors)
PENALTY_GRIP_TIRES: float = 0.4
PENALTY_BRAKE_FADE: float = 0.6
PENALTY_POWER_OVERHEAT: float = 0.5
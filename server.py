"""
CA-Racing Multiplayer Server
============================
Asynchronous game server with Room-Lobby architecture and lag compensation.

✅ v3.0 - MULTIPLAYER ENHANCEMENTS:
- 6 static rooms with map rotation
- Waypoint-based ranking system
- 30Hz physics synchronization
- Real-time leaderboard updates
- Player data injection from settings.json

Architecture:
- 6 fixed rooms, max 2 players per room
- Room states: WAITING, RACING, FINISHED
- Waypoint tracking with distance-based tiebreakers
- Client-Side Prediction with Server Reconciliation
- Authoritative server physics
"""

import asyncio
import json
import time
import math
import os
import struct
import zlib
import traceback
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
import logging
import sys

# Initialize pygame headless if available
try:
    import pygame
    os.environ["SDL_VIDEODRIVER"] = "dummy"
    pygame.init()
except ImportError:
    pass

# Fix imports path for Linux/Docker environments
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
if os.path.exists(os.path.join(current_dir, 'src')):
    sys.path.append(os.path.join(current_dir, 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

try:
    try:
        from src.network.protocol import PacketBuilder, PacketType
        logger.info("Successfully imported protocol from src.network.protocol")
    except ImportError as e1:
        logger.warning(f"Failed to import from src.network.protocol: {e1}")
        try:
            from network.protocol import PacketBuilder, PacketType
            logger.info("Successfully imported protocol from network.protocol")
        except ImportError as e2:
            logger.warning(f"Failed to import from network.protocol: {e2}")
            raise e2
    HAS_PROTOCOL = True
except ImportError as e:
    HAS_PROTOCOL = False
    logger.error(f"Could not import src.network.protocol or network.protocol. Binary sending disabled. Error: {e}")
    traceback.print_exc()


class RoomState(Enum):
    """Possible room states"""
    EMPTY = "Empty"
    NOT_READY = "NotReady"
    READY = "Ready"
    RACING = "Racing"


@dataclass
class PlayerInput:
    """Client input data structure"""
    throttle: float  # 0.0 to 1.0
    steering: float  # -1.0 to 1.0
    is_braking: float  # 0.0 to 1.0
    timestamp: float  # Client timestamp
    sequence_number: int  # Input sequence for reconciliation


@dataclass
class PlayerState:
    """Authoritative player state with car_model"""
    player_id: str
    x: float
    y: float
    angle: float
    velocity_x: float
    velocity_y: float
    timestamp: float
    sequence_number: int
    lap: int = 0
    current_waypoint: int = 0
    finished: bool = False
    car_model: str = "car_0"


class Player:
    """Represents a connected player with car_model and race progress"""
    
    def __init__(
        self,
        player_id: str,
        writer: asyncio.StreamWriter,
        room_id: Optional[int] = None
    ):
        self.player_id: str = player_id
        self.writer: asyncio.StreamWriter = writer
        self.room_id: Optional[int] = room_id
        self.ready: bool = False
        self.car_model: str = "car_0"
        self.player_name: str = "Player"  # Set via handshake
        self.binary_mode: bool = False
        
        # Game state
        self.x: float = 0.0
        self.y: float = 0.0
        self.angle: float = 0.0
        self.velocity_x: float = 0.0
        self.velocity_y: float = 0.0
        self.lap: int = 0
        self.current_waypoint: int = 0
        self.finished: bool = False
        self.is_drifting: bool = False
        
        # Input buffer for processing
        self.last_input: Optional[PlayerInput] = None
        self.last_sequence: int = 0
        
    def get_state(self, timestamp: float, sequence: int) -> PlayerState:
        """Returns current authoritative state with car_model"""
        return PlayerState(
            player_id=self.player_id,
            x=self.x,
            y=self.y,
            angle=self.angle,
            velocity_x=self.velocity_x,
            velocity_y=self.velocity_y,
            timestamp=timestamp,
            sequence_number=sequence,
            lap=self.lap,
            current_waypoint=self.current_waypoint,
            finished=self.finished,
            car_model=self.car_model
        )


class Room:
    """Represents a game room with max 2 players and waypoint tracking"""
    
    MAX_PLAYERS = 2
    TICK_RATE = 60  # Server simulation rate (Hz)
    PHYSICS_BROADCAST_RATE = 30  # Physics sync rate (Hz)
    HEARTBEAT_TIMEOUT = 30.0  # Seconds before disconnecting inactive player
    HEARTBEAT_INTERVAL = 5.0  # Seconds between heartbeat checks
    
    def __init__(self, room_id: int, name: str, track_name: str = "map_0.tmx", track_id: str = "track_0"):
        self.room_id: int = room_id
        self.name: str = name
        self.host_name: str = "System"  # Default for static rooms
        self.track_name: str = track_name
        self.track_id: str = track_id
        self.players: Dict[str, Player] = {}
        self.state: RoomState = RoomState.EMPTY
        self.sequence_number: int = 0
        self.start_time: float = 0.0
        self.race_task: Optional[asyncio.Task] = None
        self.physics_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.host_id: Optional[str] = None
        self.player_last_seen: Dict[str, float] = {}
        
        # Racing Line system
        self.racing_line: List[Tuple[float, float]] = []
        self.total_track_length: float = 0.0
        self.load_racing_line()
        
    def add_player(self, player: Player) -> bool:
        """Add a player to the room"""
        if len(self.players) >= self.MAX_PLAYERS:
            return False
        
        self.players[player.player_id] = player
        player.room_id = self.room_id
        self.player_last_seen[player.player_id] = time.time()
        
        # First player becomes host
        if len(self.players) == 1 and self.host_id is None:
            self.host_id = player.player_id
            self.host_name = player.player_id[:12]
        
        # Update state
        if self.state == RoomState.EMPTY:
            self.state = RoomState.NOT_READY
        
        logger.info(f"Player {player.player_id} joined Room {self.room_id} ({len(self.players)}/{self.MAX_PLAYERS})")
        return True
    
    def remove_player(self, player_id: str) -> None:
        """Remove a player from the room"""
        if player_id in self.players:
            del self.players[player_id]
            if player_id in self.player_last_seen:
                del self.player_last_seen[player_id]
            
            # Reset host if current host left
            if self.host_id == player_id:
                if self.players:
                    # Assign new host
                    self.host_id = list(self.players.keys())[0]
                    self.host_name = self.players[self.host_id].player_id[:12]
                else:
                    # Room is empty, reset to System
                    self.host_id = None
                    self.host_name = "System"
                    self.state = RoomState.EMPTY
            
            logger.info(f"Player {player_id} left Room {self.room_id} ({len(self.players)}/{self.MAX_PLAYERS})")
    
    def is_full(self) -> bool:
        """Check if room is full"""
        return len(self.players) >= self.MAX_PLAYERS
    
    def is_empty(self) -> bool:
        """Check if room is empty"""
        return len(self.players) == 0
    
    async def start_heartbeat_monitor(self):
        """Monitor player connections with heartbeats"""
        try:
            while len(self.players) > 0:
                await asyncio.sleep(self.HEARTBEAT_INTERVAL)
                current_time = time.time()
                inactive_players = []
                
                # Check for timeouts
                for player_id, last_seen in list(self.player_last_seen.items()):
                    if current_time - last_seen > self.HEARTBEAT_TIMEOUT:
                        inactive_players.append(player_id)
                        logger.warning(f"Player {player_id} timed out (no heartbeat response)")
                
                # Remove inactive players
                for player_id in inactive_players:
                    if player_id in self.players:
                        player = self.players[player_id]
                        if not player.writer.is_closing():
                            player.writer.close()
                            await player.writer.wait_closed()

                # Send heartbeat request to all players
                if self.players:
                    heartbeat_message = {'type': 'heartbeat_request', 'timestamp': time.time()}
                    tasks = [
                        asyncio.create_task(
                            player.writer.write((json.dumps(heartbeat_message) + '\n').encode())
                        )
                        for player in self.players.values() if not player.writer.is_closing()
                    ]
                    if tasks:
                        await asyncio.gather(*tasks, return_exceptions=True)
                        
        except asyncio.CancelledError:
            logger.info(f"Heartbeat monitor cancelled for Room {self.room_id}")
        except Exception as e:
            logger.error(f"Error in heartbeat monitor: {e}")
    
    def load_racing_line(self):
        """
        Load racing line from TMX map file using pytmx.
        Uses 'RacingLine' object layer and 'Centerline' object (polyline).
        """
        self.racing_line = []
        self.total_track_length = 0.0
        
        try:
            # Try to import pytmx
            try:
                from pytmx import TiledMap, TiledObject
                has_pytmx = True
            except ImportError:
                logger.warning("pytmx not installed, using fallback racing line")
                has_pytmx = False
            
            if has_pytmx:
                # determine path
                possible_paths = [
                    f"maps/{self.track_name}",
                    f"assets/maps/{self.track_name}",
                    f"/opt/ca-racing-server/maps/{self.track_name}",
                    self.track_name
                ]
                map_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        map_path = path
                        break
                
                if map_path:
                    try:
                        tmx_data = TiledMap(map_path)
                        racing_line_layer = None
                        for layer in tmx_data.layers:
                            if hasattr(layer, 'name') and layer.name == 'RacingLine':
                                racing_line_layer = layer
                                break
                        
                        if racing_line_layer:
                            # Find Centerline object
                            centerline = None
                            for obj in racing_line_layer:
                                if obj.name == 'Centerline':
                                    centerline = obj
                                    break
                            
                            # Fallback if specific object not found but layer exists
                            if not centerline and len(racing_line_layer) > 0:
                                centerline = racing_line_layer[0]

                            if centerline:
                                # Extract points
                                if hasattr(centerline, 'points'):
                                    # Normalize points to list of tuples
                                    self.racing_line = [( float(p.x if hasattr(p, 'x') else p[0]), 
                                                          float(p.y if hasattr(p, 'y') else p[1]) ) 
                                                        for p in centerline.points]
                                    
                                    # Calculate total length
                                    current_dist = 0.0
                                    for i in range(len(self.racing_line) - 1):
                                        p1 = self.racing_line[i]
                                        p2 = self.racing_line[i+1]
                                        dist = math.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
                                        current_dist += dist
                                    self.total_track_length = current_dist
                                    
                                    logger.info(f"Room {self.room_id}: Loaded racing line with {len(self.racing_line)} points (Length: {self.total_track_length:.1f})")
                                    return
                        
                        logger.warning(f"Room {self.room_id}: 'RacingLine' layer or 'Centerline' object not found in {map_path}")
                    
                    except Exception as e:
                        logger.error(f"Room {self.room_id}: Failed to parse TMX {map_path}: {e}")
        
        except Exception as e:
            logger.error(f"Room {self.room_id}: Error loading racing line: {e}")
            
        # Fallback: Simple loop
        logger.warning(f"Room {self.room_id}: Using fallback racing line (circle)")
        cx, cy = 400, 300
        radius = 200
        steps = 20
        self.racing_line = []
        for i in range(steps):
             theta = (2 * math.pi * i) / steps
             self.racing_line.append((cx + radius * math.cos(theta), cy + radius * math.sin(theta)))
        self.total_track_length = 2 * math.pi * radius

    def calculate_rankings(self) -> List[Dict]:
        """
        Calculate race rankings based on Racing Line progress.
        
        Position = Laps + (DistanceAlongLine / TotalLength)
        """
        if not self.players or not self.racing_line:
            return []
            
        player_scores = []
        
        for player_id, player in self.players.items():
            # Find closest point on racing line to player
            min_dist_sq = float('inf')
            closest_idx = 0
            
            # Simple linear search for closest point (optimization: search around last known index)
            for i, p in enumerate(self.racing_line):
                d2 = (player.x - p[0])**2 + (player.y - p[1])**2
                if d2 < min_dist_sq:
                    min_dist_sq = d2
                    closest_idx = i
            
            # Approximate progress along line
            # Sum distances up to closest_idx
            dist_along = 0.0
            for i in range(closest_idx):
                p1 = self.racing_line[i]
                p2 = self.racing_line[i+1] # safe because loop goes to closest_idx < len
                dist_along += math.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
            
            # Total score
            progress = 0.0
            if self.total_track_length > 0:
                progress = dist_along / self.total_track_length
                
            score = player.lap + progress
            
            player_scores.append({
                'player_id': player_id,
                'player_name': player.player_name,  # Using injected name
                'score': score,
                'finished': player.finished
            })
            
        # Sort descending by score
        sorted_stats = sorted(player_scores, key=lambda p: p['score'], reverse=True)
        
        # Assign rank
        results = []
        for i, p in enumerate(sorted_stats):
            results.append({
                'name': p['player_name'],
                'pos': i + 1,
                'player_id': p['player_id'] # helpful for client to know which is them
            })
            
        return results
    
    async def start_physics_broadcast(self):
        """
        Broadcast physics updates at 30Hz during race.
        
        This sends:
        - All player positions, velocities, and states
        - Current race rankings
        """
        interval = 1.0 / self.PHYSICS_BROADCAST_RATE
        
        try:
            while self.state == RoomState.RACING and len(self.players) > 0:
                start_time = time.time()
                
                # Collect all player states
                states = {}
                for player_id, player in self.players.items():
                    states[player_id] = {
                        'x': player.x,
                        'y': player.y,
                        'angle': player.angle,
                        'velocity_x': player.velocity_x,
                        'velocity_y': player.velocity_y,
                        'is_drifting': player.is_drifting,
                        'lap': player.lap,
                        'current_waypoint': player.current_waypoint,
                        'finished': player.finished
                    }
                
                # Broadcast physics sync
                await self.broadcast_to_all({
                    'type': 'physics_sync',
                    'timestamp': time.time(),
                    'states': states
                })
                
                # Calculate and broadcast rankings
                rankings = self.calculate_rankings()
                await self.broadcast_to_all({
                    'type': 'rankings',
                    'data': rankings,
                    'timestamp': time.time()
                })
                
                # Wait for next tick
                elapsed = time.time() - start_time
                sleep_time = max(0, interval - elapsed)
                await asyncio.sleep(sleep_time)
                
        except asyncio.CancelledError:
            logger.info(f"Physics broadcast cancelled for Room {self.room_id}")
        except Exception as e:
            logger.error(f"Error in physics broadcast: {e}")
    
    async def broadcast_to_all(self, message: Dict):
        """Broadcast message to all players in room"""
        tasks = []
        for player in self.players.values():
            if not player.writer.is_closing():
                try:
                    data = json.dumps(message) + '\\n'
                    player.writer.write(data.encode())
                    tasks.append(player.writer.drain())
                except Exception as e:
                    logger.error(f"Failed to send to {player.player_id}: {e}")
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)


class RoomManager:
    """Manages all game rooms with 6 static rooms"""
    
    MAX_DISPLAYED_ROOMS = 20
    NUM_STATIC_ROOMS = 6  # ✅ 6 rooms with rotating maps
    
    def __init__(self):
        self.rooms: Dict[int, Room] = {}
        self.player_lookup: Dict[str, int] = {}
        self.next_room_id = 0
        self.room_creation_order: List[int] = []
        
        # ✅ FIX: Initialize 5 static rooms with map_0
        self._initialize_static_rooms()

    def _initialize_static_rooms(self):
        """✅ Create 6 static rooms at server startup with rotating maps"""
        logger.info("=" * 60)
        logger.info("Initializing static rooms with map rotation...")
        logger.info("=" * 60)
        
        # Scan maps directory for available tracks
        maps_dir = 'maps'
        map_files = []
        if os.path.exists(maps_dir):
            map_files = sorted([f for f in os.listdir(maps_dir) if f.endswith('.tmx')])
        
        if not map_files:
            logger.warning("No TMX maps found, using default map_0.tmx")
            map_files = ['map_0.tmx']
        
        logger.info(f"Found {len(map_files)} maps: {', '.join(map_files)}")
        
        for i in range(self.NUM_STATIC_ROOMS):
            room_id = self.next_room_id
            self.next_room_id += 1
            
            room_name = f"Room {i + 1}"
            # ✅ Map rotation: room_index % len(map_list)
            track_name = map_files[i % len(map_files)]
            track_id = f"track_{i % len(map_files)}"
            
            room = Room(room_id, room_name, track_name, track_id)
            self.rooms[room_id] = room
            self.room_creation_order.append(room_id)
            
            logger.info(f"  ✓ Created {room_name} (ID: {room_id}) - Map: {track_name}")
        
        logger.info("=" * 60)
        logger.info(f"✅ Successfully initialized {self.NUM_STATIC_ROOMS} static rooms")
        logger.info("=" * 60)

    def create_room(self, name: str, host_name: str = "Unknown", track_name: str = "map_0.tmx") -> Room:
        """Creates a new dynamic room (in addition to static rooms)"""
        room_id = self.next_room_id
        self.next_room_id += 1
        room = Room(room_id, name, track_name, "track_0")
        room.host_name = host_name
        self.rooms[room_id] = room
        self.room_creation_order.append(room_id)
        room.host_id = None
        logger.info(f"Room {room_id} created: {name} (host: {host_name})")
        return room

    def get_room(self, room_id: int) -> Optional[Room]:
        """Get room by ID"""
        return self.rooms.get(room_id)

    def get_player_room(self, player_id: str) -> Optional[Room]:
        """Get room containing player"""
        room_id = self.player_lookup.get(player_id)
        if room_id is not None:
            return self.rooms.get(room_id)
        return None

    def join_room(self, player: Player, room_id: int) -> bool:
        """Add a player to a specific room"""
        room = self.get_room(room_id)
        if room and room.add_player(player):
            self.player_lookup[player.player_id] = room_id
            if len(room.players) == 1:
                room.host_id = player.player_id 
            return True
        return False

    def leave_room(self, player_id: str) -> None:
        """Remove a player from their room"""
        room = self.get_player_room(player_id)
        if room:
            room_id = room.room_id
            room.remove_player(player_id)
            
            # ✅ FIX: Don't delete static rooms (first NUM_STATIC_ROOMS)
            # Only delete dynamically created rooms when empty
            if room_id >= self.NUM_STATIC_ROOMS and not room.players:
                del self.rooms[room_id]
                if room_id in self.room_creation_order:
                    self.room_creation_order.remove(room_id)
                logger.info(f"Dynamic room {room_id} deleted (was empty)")
            
            if player_id in self.player_lookup:
                del self.player_lookup[player_id]

    def get_room_list(self, limit: int = None, page: int = 0) -> List[Dict]:
        """
        Returns a list of rooms for the lobby with pagination.
        
        Args:
            limit: Maximum number of rooms to return
            page: Page number for pagination (0-indexed)
        
        Returns:
            List of room dictionaries, most recent first
        """
        if limit is None:
            limit = self.MAX_DISPLAYED_ROOMS
        
        # Get most recent rooms (reverse order)
        recent_room_ids = list(reversed(self.room_creation_order))
        
        # Calculate pagination
        start_idx = page * limit
        end_idx = start_idx + limit
        paginated_ids = recent_room_ids[start_idx:end_idx]
        
        # Build room list
        room_list = []
        for room_id in paginated_ids:
            room = self.rooms.get(room_id)
            if room:
                # Get host name
                host_name = getattr(room, 'host_name', 'System')
                if not host_name and room.players:
                    first_player = list(room.players.values())[0]
                    host_name = first_player.player_id[:12]
                
                # Safely get state value
                state_val = "waiting"
                try:
                    if hasattr(room.state, 'value'):
                        state_val = room.state.value
                    else:
                        state_val = str(room.state)
                except Exception:
                    pass

                room_list.append({
                    "room_id": room.room_id,
                    "name": room.name,
                    "players_count": len(room.players),
                    "max_players": room.MAX_PLAYERS,
                    "host_name": host_name,
                    "track_name": getattr(room, 'track_name', 'map_0.tmx'),
                    "state": state_val,
                    "is_full": room.is_full(),
                    "is_empty": len(room.players) == 0
                })
        
        return room_list
    
    def get_room_count(self) -> int:
        """Get total number of rooms"""
        return len(self.rooms)


class GameServer:
    """Main multiplayer game server"""
    
    def __init__(self, host: str = '0.0.0.0', port: int = 8443):
        self.host = host
        self.port = port
        self.room_manager = RoomManager()
        self.clients: Dict[asyncio.StreamWriter, Player] = {}
        self.lobby_subscribers: Set[Player] = set()

    async def start(self):
        """Start the server"""
        server = await asyncio.start_server(
            self.handle_client,
            self.host,
            self.port
        )
        
        addr = server.sockets[0].getsockname()
        logger.info(f'🎮 CA-Racing Server running on {addr}')
        logger.info(f'📊 Server has {self.room_manager.get_room_count()} rooms ready')
        
        async with server:
            await server.serve_forever()
    
    def _map_packet_type(self, p_type: int) -> str:
        """Map binary packet type to string"""
        mapping = {
            4: 'heartbeat',
            10: 'ready',
            14: 'request_room_list',
            15: 'create_room',
            16: 'join_room',
            17: 'leave_room',
            18: 'chat_message',
            19: 'spectate_room',
            22: 'input',
            30: 'ping',
            0: 'client_hello',
            5: 'client_info',
        }
        return mapping.get(p_type, f'unknown_{p_type}')

    async def receive_message(self, reader: asyncio.StreamReader) -> Tuple[Optional[Dict], bool]:
        """Receive message handling both text (JSON lines) and binary (PacketBuilder) protocols"""
        try:
            # Read first byte to determine protocol
            first_byte = await reader.read(1)
            if not first_byte:
                return None, False
            
            if first_byte == b'{':
                # Text protocol (JSON lines)
                line_data = await reader.readline()
                full_line = first_byte + line_data
                try:
                    return json.loads(full_line.decode('utf-8')), False
                except json.JSONDecodeError:
                    logger.error(f"JSON decode error: {full_line}")
                    return None, False
            else:
                # Binary protocol (PacketBuilder)
                # We already read 1 byte (PacketType), need 5 more for header
                rest_of_header = await reader.readexactly(5)
                header_data = first_byte + rest_of_header
                
                # Unpack header: Type (1), Compressed (1), Length (4)
                p_type, compressed, length = struct.unpack('!BBI', header_data)
                
                # Read payload
                payload_data = await reader.readexactly(length)
                
                # Decompress
                if compressed:
                    payload_data = zlib.decompress(payload_data)
                
                # Parse JSON
                try:
                    message = json.loads(payload_data.decode('utf-8'))
                except UnicodeDecodeError:
                    logger.warning(f"Failed to decode binary payload")
                    return {}, True
                
                # Inject 'type' if missing
                if 'type' not in message:
                    message['type'] = self._map_packet_type(p_type)
                
                return message, True
                
        except (asyncio.IncompleteReadError, ConnectionResetError):
            return None, False
        except Exception as e:
            logger.error(f"Receive error: {e}")
            return None, False

    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle new client connection"""
        addr = writer.get_extra_info('peername')
        player_id = f"player_{addr[0]}_{addr[1]}_{int(time.time() * 1000) % 100000}"
        player = None
        
        try:
            logger.info(f"New connection from {addr}, assigned ID: {player_id}")
            
            player = Player(player_id, writer)
            self.clients[writer] = player
            
            # Send connection acknowledgment
            await self.send_message(writer, {
                'type': 'connected',
                'player_id': player_id,
                'timestamp': time.time()
            })

            # Wait for 'client_hello' handshake
            try:
                # ✅ FIX: Use robust receive_message instead of readline
                # Unpack tuple: message, is_binary
                hello_data_tuple = await asyncio.wait_for(self.receive_message(reader), timeout=5.0)
                hello_message, is_binary = hello_data_tuple
                
                if not hello_message:
                    logger.warning(f"{player_id} disconnected before handshake.")
                    return

                # Set binary mode if detected
                if is_binary:
                    if not HAS_PROTOCOL:
                        logger.error(f"Player {player_id} tried BINARY protocol but server lacks protocol definitions. Rejecting.")
                        return

                    player.binary_mode = True
                    logger.info(f"Player {player_id} using BINARY protocol")

                if hello_message.get('type') != 'client_hello':
                    # Allow client_info as well just in case
                    if hello_message.get('type') != 'client_info':
                        logger.warning(f"Invalid handshake from {player_id}: {hello_message.get('type')}")
                        return
                
                # ✅ Extract player data from handshake
                player.player_name = hello_message.get('player_name', 'Player')
                player.car_model = hello_message.get('car_id', 'car_0')
                
                logger.info(f"Handshake complete for {player_id} ({player.player_name}, car: {player.car_model})")
                self.lobby_subscribers.add(player)
            
            except (asyncio.TimeoutError, json.JSONDecodeError) as e:
                logger.error(f"Handshake failed for {player_id}: {e}")
                return
            except ConnectionResetError:
                logger.warning(f"Connection reset during handshake for {player_id}")
                return

            # Send initial room list to the new client
            await self.send_room_list(player)
            
            while True:
                # ✅ FIX: Use robust receive_message
                message_tuple = await self.receive_message(reader)
                message, is_binary = message_tuple
                
                if not message:
                    break
                
                # Update binary mode if it changed (unlikely but possible if mixed)
                if is_binary and not player.binary_mode:
                    player.binary_mode = True
                
                # [DEBUG] Log received message
                msg_type = message.get('type', 'unknown')
                # logger.info(f"[DEBUG] Raw message from {player_id}: {msg_type} | Payload: {str(message)[:100]}...")
                
                await self.handle_client_message(player, message)
        
        except asyncio.CancelledError:
            logger.info(f"Client {player_id} handler cancelled")
        except ConnectionResetError:
            logger.warning(f"Client {player_id} connection reset by peer")
        except Exception as e:
            logger.error(f"Client {player_id} error: {e}")
        finally:
            await self.disconnect_player(player)

    async def handle_client_message(self, player: Player, message: Dict):
        """Handle client messages"""
        msg_type = message.get('type')
        room = self.room_manager.get_player_room(player.player_id)
        
        if msg_type == 'heartbeat':
            # Update last seen time
            if room:
                room.player_last_seen[player.player_id] = time.time()
        
        elif msg_type == 'request_room_list':
            logger.info(f"Player {player.player_id} requested room list")
            await self.send_room_list(player)
        
        elif msg_type == 'create_room':
            # Create new dynamic room
            # room_name = message.get('room_name', f'Room {self.room_manager.next_room_id}')
            # host_name = message.get('host_name', player.player_id[:12])
            # track_name = message.get('track_name', 'map_0.tmx')
            
            # new_room = self.room_manager.create_room(room_name, host_name, track_name)
            
            # # Auto-join creator
            # if self.room_manager.join_room(player, new_room.room_id):
            #     self.lobby_subscribers.remove(player)
                
            #     await self.send_message(player.writer, {
            #         'type': 'room_assigned',
            #         'room_id': new_room.room_id,
            #         'timestamp': time.time()
            #     })
            #     logger.info(f"Player {player.player_id} created and joined room {new_room.room_id}")
            #     await self.broadcast_room_update(new_room)
            pass
        
        elif msg_type == 'join_room':
            # Join existing room
            room_id = message.get('room_id')
            target_room = self.room_manager.get_room(room_id)
            
            if target_room is None:
                await self.send_message(player.writer, {
                    'type': 'error',
                    'message': 'Room does not exist',
                    'timestamp': time.time()
                })
            elif target_room.is_full():
                await self.send_message(player.writer, {
                    'type': 'error',
                    'message': 'Room is full',
                    'timestamp': time.time()
                })
            else:
                if room:
                    self.room_manager.leave_room(player.player_id)
                
                if self.room_manager.join_room(player, room_id):
                    self.lobby_subscribers.remove(player)
                    
                    await self.send_message(player.writer, {
                        'type': 'room_assigned',
                        'room_id': room_id,
                        'timestamp': time.time()
                    })
                    
                    await self.broadcast_to_room(target_room, {
                        'type': 'player_joined',
                        'player_id': player.player_id,
                        'timestamp': time.time()
                    })
                    
                    await self.send_room_state(target_room)
                    await self.broadcast_room_update(target_room)
        
        elif msg_type == 'leave_room':
            if room:
                room_id = room.room_id
                await self.broadcast_to_room(room, {
                    'type': 'player_left',
                    'player_id': player.player_id,
                    'timestamp': time.time()
                }, exclude=player.player_id)
                
                self.room_manager.leave_room(player.player_id)
                self.lobby_subscribers.add(player)
                
                await self.send_message(player.writer, {
                    'type': 'left_room',
                    'timestamp': time.time()
                })
                
                # ✅ FIX: Use send_room_list instead of non-existent send_full_room_list
                await self.send_room_list(player)
                await self.broadcast_room_update(self.room_manager.get_room(room_id))
        
        elif msg_type == 'ready':
            if room:
                player.ready = True
                logger.info(f"Player {player.player_id} is ready in Room {room.room_id}")
                
                await self.send_room_state(room)
                
                # Check if all players ready
                if all(p.ready for p in room.players.values()) and len(room.players) >= 1:
                    room.state = RoomState.READY
                    await self.send_room_state(room)
                    await asyncio.sleep(1) # Brief pause before start
                    await self.start_race(room)
                else:
                    # If not all ready, ensure state is NOT_READY (unless already racing)
                    if room.state != RoomState.RACING:
                        room.state = RoomState.NOT_READY
                        await self.send_room_state(room)
        
        elif msg_type == 'input':
            if room and room.state == RoomState.RACING:
                player.last_input = PlayerInput(
                    throttle=message.get('throttle', 0.0),
                    steering=message.get('steering', 0.0),
                    is_braking=message.get('is_braking', 0.0),
                    timestamp=message.get('timestamp', time.time()),
                    sequence_number=message.get('sequence_number', 0)
                )
                player.last_sequence = player.last_input.sequence_number
        
        elif msg_type == 'physics_state':
            if room:
                # ✅ Update player physics state from client prediction
                player.x = message.get('x', player.x)
                player.y = message.get('y', player.y)
                player.angle = message.get('angle', player.angle)
                player.velocity_x = message.get('velocity_x', player.velocity_x)
                player.velocity_y = message.get('velocity_y', player.velocity_y)
                player.is_drifting = message.get('is_drifting', False)
                player.lap = message.get('lap', player.lap)
                player.current_waypoint = message.get('current_waypoint', player.current_waypoint)

    def _get_packet_type_from_string(self, msg_type: str) -> Optional[int]:
        """Map string message type to binary PacketType int"""
        mapping = {
            'heartbeat': 4,
            'room_list': 14,
            'room_assigned': 1,
            'player_joined': 2,
            'player_left': 3,
            'connected': 0, # WELCOME
            'race_start': 11,
            'room_state': 20, # Mapping to ROOM_UPDATED
            'room_data_update': 21,
            'chat_message': 18,
            'error': 40,
        }
        return mapping.get(msg_type)

    async def send_message(self, writer: asyncio.StreamWriter, message: Dict):
        """Send message to a single client (Text/Binary auto-detect)"""
        if writer.is_closing():
            return
        
        # Determine protocol mode
        use_binary = False
        if writer in self.clients:
            use_binary = self.clients[writer].binary_mode
            
        try:
            if use_binary and HAS_PROTOCOL:
                msg_type_str = message.get('type')
                packet_type_int = self._get_packet_type_from_string(msg_type_str)
                
                if packet_type_int is not None:
                    packet = PacketBuilder.build(packet_type_int, message)
                    writer.write(packet)
                    await writer.drain()
                    return
                # If no mapping, fall back to text (better than nothing)
            
            data = json.dumps(message) + '\n'
            writer.write(data.encode())
            await writer.drain()
        except Exception as e:
            logger.error(f"Error sending message: {e}")

    async def broadcast_to_room(self, room: Room, message: Dict, exclude: str = None):
        """Broadcast message to all players in a room"""
        tasks = []
        for player_id, player in room.players.items():
            if player_id != exclude:
                tasks.append(self.send_message(player.writer, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def send_room_list(self, player: Player):
        """Send the entire list of rooms to a single player."""
        # ✅ FIX: Send as a single consolidated message instead of multiple rapid messages
        # This prevents TCP buffer concatenation issues that cause UnicodeDecodeError
        rooms_data = []
        for room in self.room_manager.rooms.values():
            rooms_data.append({
                'room_id': room.room_id,
                'room_name': room.name,
                'map_name': room.track_name,
                'current_players': len(room.players),
                'max_players': room.MAX_PLAYERS,
                'status': room.state.value
            })

        logger.info(f"Sending room list to {player.player_id}: {len(rooms_data)} rooms")

        # Log room details for debugging
        for room in rooms_data:
            logger.info(f"  Room {room['room_id']}: {room['room_name']} ({room['current_players']}/{room['max_players']}) - {room['status']}")

        # Send as a single 'room_list' message
        message = {
            'type': 'room_list',
            'rooms': rooms_data,
            'total_rooms': len(rooms_data),
            'page': 0,
            'timestamp': time.time()
        }
        logger.info(f"Sending room_list message: {message}")
        await self.send_message(player.writer, message)

    async def broadcast_room_update(self, room: Room):
        """Broadcast an update for a single room to all lobby subscribers."""
        if room is None:
            return
        message = {
            'type': 'room_data_update',
            'room_id': room.room_id,
            'room_name': room.name,
            'map_name': room.track_name,
            'current_players': len(room.players),
            'max_players': room.MAX_PLAYERS,
            'status': room.state.value,
            'timestamp': time.time()
        }
        tasks = [self.send_message(p.writer, message) for p in self.lobby_subscribers]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def send_room_state(self, room: Room):
        """Send room state to all players in room"""
        players_info = [
            {
                'player_id': pid,
                'ready': p.ready,
                'car_model': p.car_model
            }
            for pid, p in room.players.items()
        ]
        
        message = {
            'type': 'room_state',
            'room_id': room.room_id,
            'players': players_info,
            'state': room.state.value if hasattr(room.state, 'value') else str(room.state),
            'timestamp': time.time()
        }
        
        await self.broadcast_to_room(room, message)

    async def start_race(self, room: Room):
        """Start a race in a room"""
        room.state = RoomState.RACING
        room.start_time = time.time()
        
        logger.info(f"Starting race in Room {room.room_id}")
        
        await self.broadcast_to_room(room, {
            'type': 'race_start',
            'room_id': room.room_id,
            'track_id': room.track_id,
            'timestamp': room.start_time
        })
        
        await self.broadcast_room_update(room)

    async def disconnect_player(self, player: Optional[Player]):
        """Disconnect a player"""
        if player is None:
            return
        
        player_id = player.player_id
        
        # Remove from room
        room = self.room_manager.get_player_room(player_id)
        if room:
            room_id = room.room_id
            await self.broadcast_to_room(room, {
                'type': 'player_left',
                'player_id': player_id,
                'timestamp': time.time()
            }, exclude=player_id)
            
            self.room_manager.leave_room(player_id)
            await self.broadcast_room_update(self.room_manager.get_room(room_id))

        # Remove from lobby subscribers
        if player in self.lobby_subscribers:
            self.lobby_subscribers.remove(player)
        
        # Remove from clients
        if player.writer in self.clients:
            del self.clients[player.writer]
        
        # Close connection
        if not player.writer.is_closing():
            player.writer.close()
            await player.writer.wait_closed()
        
        logger.info(f"Player {player_id} disconnected")


async def main():
    """Main entry point"""
    server = GameServer(host='0.0.0.0', port=8444)
    try:
        await server.start()
    except KeyboardInterrupt:
        logger.info("Server shutting down...")


if __name__ == "__main__":
    asyncio.run(main())